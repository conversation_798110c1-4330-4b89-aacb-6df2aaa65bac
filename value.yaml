replicaCount: 1

image:
  # override docker image tag, which defaults to appVersion defined in Chart.yaml
  # tag: 10-beta.1

  repository: 639966507663.dkr.ecr.eu-west-2.amazonaws.com/quest-policy-integration-service
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: "zone-data-service"

service:
  type: ClusterIP
  port: 80

app:
  containerPort: 8080
  envs: |
  #    - name: service.policies-search.url
  #      value: http://policies-search-service/policies-search/api/v1
  secretWithEnvs:
    create: false # please consider that creating secret is possible if secreted with name provided does not already exists
    name: "" # when name is not provided, deployment name is used
    envs: { }
    # ENV_NAME:
    #   key: key
    #   value: value

livenessProbe:
   enabled: true
   path: /zone-data/manage/health
   initialDelaySeconds: 180 #60
   periodSeconds: 20
   timeoutSeconds: 10 #5
   successThreshold: 1
   failureThreshold: 3

readinessProbe:
   enabled: true
   path: /zone-data/manage/health
   initialDelaySeconds: 180 #30
   periodSeconds: 20
   timeoutSeconds: 10 #3
   successThreshold: 1
   failureThreshold: 3

resources:
  limits:
    cpu: 500m
    memory: 1Gi
  requests:
    cpu: 100m
    memory: 256Mi

nodeSelector: {}

tolerations: []

affinity: {}