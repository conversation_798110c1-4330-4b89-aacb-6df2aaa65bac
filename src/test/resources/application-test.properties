# Test properties for ZoneDataServiceApplicationTest
spring.main.web-application-type=none
spring.data.mongodb.uri=mongodb://localhost:27017/testdb
spring.data.mongodb.auto-index-creation=true

# Disable metrics for tests
management.metrics.export.statsd.enabled=false
management.metrics.export.defaults.enabled=false
management.endpoints.web.exposure.include=health,info,prometheus

# Disable scheduling for tests
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration

# Disable JPA for tests
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# Disable Flyway for tests
spring.flyway.enabled=false

# Disable Liquibase for tests
spring.liquibase.enabled=false
