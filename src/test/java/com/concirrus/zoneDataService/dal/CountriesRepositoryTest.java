package com.concirrus.zoneDataService.dal;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.mongodb.core.MongoTemplate;
import com.concirrus.zoneDataService.model.Country;
import java.util.Collections;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class CountriesRepositoryTest {
    @Mock
    private MongoTemplate mongoTemplate;
    @InjectMocks
    private CountriesRepository repo;

    @BeforeEach
    void setUp() { MockitoAnnotations.openMocks(this); }

    @Test
    void testListCountries() {
        List<Country> expected = Collections.singletonList(new Country("India", "IN"));
        when(mongoTemplate.find(any(), eq(Country.class))).thenReturn(expected);
        List<Country> result = repo.listCountries("foo", 0, 10);
        assertEquals(1, result.size());
    }

    @Test
    void testListCountries_Empty() {
        when(mongoTemplate.find(any(), eq(Country.class))).thenReturn(Collections.emptyList());
        List<Country> result = repo.listCountries("bar", 0, 10);
        assertTrue(result.isEmpty());
    }

    @Test
    void testListCountries_Null() {
        when(mongoTemplate.find(any(), eq(Country.class))).thenReturn(null);
        List<Country> result = repo.listCountries("baz", 0, 10);
        assertNull(result);
    }

    @Test
    void testListCountries_NegativePage() {
        when(mongoTemplate.find(any(), eq(Country.class))).thenReturn(Collections.emptyList());
        List<Country> result = repo.listCountries("foo", -1, 10);
        assertTrue(result.isEmpty());
    }

    @Test
    void testListCountries_ZeroSize() {
        when(mongoTemplate.find(any(), eq(Country.class))).thenReturn(Collections.emptyList());
        List<Country> result = repo.listCountries("foo", 0, 0);
        assertTrue(result.isEmpty());
    }

    @Test
    void testCountCountries() {
        when(mongoTemplate.count(any(), eq(Country.class))).thenReturn(5L);
        long count = repo.countCountries("foo");
        assertEquals(5L, count);
    }

    @Test
    void testCountCountries_Zero() {
        when(mongoTemplate.count(any(), eq(Country.class))).thenReturn(0L);
        long count = repo.countCountries("bar");
        assertEquals(0L, count);
    }

    @Test
    void testCountCountries_Negative() {
        when(mongoTemplate.count(any(), eq(Country.class))).thenReturn(-1L);
        long count = repo.countCountries("baz");
        assertEquals(-1L, count);
    }
}
