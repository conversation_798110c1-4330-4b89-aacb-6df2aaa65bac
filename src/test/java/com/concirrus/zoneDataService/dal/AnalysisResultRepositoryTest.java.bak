package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.model.AnalysisResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class AnalysisResultRepositoryTest {
    @Mock
    private MongoTemplate mongoTemplate;
    @InjectMocks
    private AnalysisResultRepository analysisResultRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testFindByBlastZoneId() {
        List<AnalysisResult> expected = Collections.singletonList(new AnalysisResult());
        when(mongoTemplate.find(any(), eq(AnalysisResult.class))).thenReturn(expected);
        List<AnalysisResult> result = analysisResultRepository.findByBlastZoneId("zone1");
        assertEquals(1, result.size());
    }

    @Test
    void testFindByBlastZoneId_Empty() {
        when(mongoTemplate.find(any(), eq(AnalysisResult.class))).thenReturn(Collections.emptyList());
        List<AnalysisResult> result = analysisResultRepository.findByBlastZoneId("zone2");
        assertTrue(result.isEmpty());
    }
}

