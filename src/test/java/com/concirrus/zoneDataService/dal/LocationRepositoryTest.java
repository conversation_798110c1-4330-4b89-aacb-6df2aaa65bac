package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.dto.LocationFilterRequest;
import com.concirrus.zoneDataService.dto.LocationResponse;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LocationRepositoryTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private LocationRepository locationRepository;

    @Mock
    private AggregationResults<LocationResponse> locationAggregationResults;
    
    @Mock
    private AggregationResults<Document> documentAggregationResults;

    @Captor
    private ArgumentCaptor<Aggregation> aggregationCaptor;

    @BeforeEach
    void setUp() {
        // No need to set up default stubs here as they're specific to each test
    }

    @Test
    void testFetchLocationResponsesByBlastZoneId() {
        // Given
        LocationFilterRequest filter = new LocationFilterRequest();
        filter.setBlastZoneId("123");
        filter.setSubmissionId("sub123");
        filter.setGeocoding("A");
        filter.setPmlZone("ZONE1");

        LocationResponse response = new LocationResponse();
        when(locationAggregationResults.getMappedResults()).thenReturn(Collections.singletonList(response));
        when(mongoTemplate.aggregate(any(Aggregation.class), eq("blast_zone_location_mapping"), eq(LocationResponse.class)))
                .thenReturn(locationAggregationResults);

        // When
        List<LocationResponse> result = locationRepository.fetchLocationResponsesByBlastZoneId(
                filter, "locationName", "ASC", 0, 10);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(response, result.get(0));

        verify(mongoTemplate).aggregate(aggregationCaptor.capture(), 
                eq("blast_zone_location_mapping"), eq(LocationResponse.class));
        
        Aggregation aggregation = aggregationCaptor.getValue();
        assertNotNull(aggregation);
        String pipeline = aggregation.toString();
        // Verify the pipeline contains the expected stages (order matters)
        String[] expectedStages = {
            "$match",
            "$lookup",
            "$unwind",
            "$project",
            "$sort",
            "$skip",
            "$limit"
        };
        
        int currentIndex = 0;
        for (String stage : expectedStages) {
            int stageIndex = pipeline.indexOf(stage, currentIndex);
            assertTrue(stageIndex >= 0, "Missing stage: " + stage);
            currentIndex = stageIndex + stage.length();
        }
    }

    @Test
    void testFetchLocationResponsesByBlastZoneId_WithoutOptionalFilters() {
        // Given
        LocationFilterRequest filter = new LocationFilterRequest();
        filter.setBlastZoneId("123");

        when(mongoTemplate.aggregate(any(Aggregation.class), eq("blast_zone_location_mapping"), eq(LocationResponse.class)))
                .thenReturn(locationAggregationResults);

        // When
        List<LocationResponse> result = locationRepository.fetchLocationResponsesByBlastZoneId(
                filter, null, null, 0, 10);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(mongoTemplate).aggregate(aggregationCaptor.capture(), 
                eq("blast_zone_location_mapping"), eq(LocationResponse.class));
        
        Aggregation aggregation = aggregationCaptor.getValue();
        assertNotNull(aggregation);
        String pipeline = aggregation.toString();
        // Verify the pipeline contains the expected stages (order matters)
        String[] expectedStages = {
            "$match",
            "$lookup",
            "$unwind",
            "$project",
            "$skip",
            "$limit"
        };
        
        int currentIndex = 0;
        for (String stage : expectedStages) {
            int stageIndex = pipeline.indexOf(stage, currentIndex);
            assertTrue(stageIndex >= 0, "Missing stage: " + stage);
            currentIndex = stageIndex + stage.length();
        }
    }

    @Test
    void testCountLocationResponsesByBlastZoneId() {
        // Given
        LocationFilterRequest filter = new LocationFilterRequest();
        filter.setBlastZoneId("123");
        filter.setSubmissionId("sub123");
        filter.setGeocoding("A");
        filter.setPmlZone("ZONE1");

        Document countDoc = new Document("total", 5);
        when(mongoTemplate.aggregate(any(Aggregation.class), eq("blast_zone_location_mapping"), eq(Document.class)))
                .thenReturn(documentAggregationResults);
        when(documentAggregationResults.getUniqueMappedResult()).thenReturn(countDoc);

        // When
        long count = locationRepository.countLocationResponsesByBlastZoneId(filter);

        // Then
        assertEquals(5L, count);
        
        verify(mongoTemplate).aggregate(aggregationCaptor.capture(), 
                eq("blast_zone_location_mapping"), eq(Document.class));
                
        Aggregation aggregation = aggregationCaptor.getValue();
        assertNotNull(aggregation);
        String pipeline = aggregation.toString();
        // Verify the pipeline contains the expected stages for count
        String[] expectedStages = {
            "$match",
            "$count"
        };
        
        int currentIndex = 0;
        for (String stage : expectedStages) {
            int stageIndex = pipeline.indexOf(stage, currentIndex);
            assertTrue(stageIndex >= 0, "Missing stage: " + stage);
            currentIndex = stageIndex + stage.length();
        }
    }

    @Test
    void testCountLocationResponsesByBlastZoneId_NoResults() {
        // Given
        LocationFilterRequest filter = new LocationFilterRequest();
        filter.setBlastZoneId("123");

        when(mongoTemplate.aggregate(any(Aggregation.class), eq("blast_zone_location_mapping"), eq(Document.class)))
                .thenReturn(documentAggregationResults);
        when(documentAggregationResults.getUniqueMappedResult()).thenReturn(null);

        // When
        long count = locationRepository.countLocationResponsesByBlastZoneId(filter);

        // Then
        assertEquals(0L, count);
        
        verify(mongoTemplate).aggregate(aggregationCaptor.capture(), 
                eq("blast_zone_location_mapping"), eq(Document.class));
                
        Aggregation aggregation = aggregationCaptor.getValue();
        assertNotNull(aggregation);
        String pipeline = aggregation.toString();
        // Verify the pipeline contains the expected stages for count
        String[] expectedStages = {
            "$match",
            "$count"
        };
        
        int currentIndex = 0;
        for (String stage : expectedStages) {
            int stageIndex = pipeline.indexOf(stage, currentIndex);
            assertTrue(stageIndex >= 0, "Missing stage: " + stage);
            currentIndex = stageIndex + stage.length();
        }
    }
    
    @Test
    void testFetchLocationResponsesByBlastZoneId_WithNullFilter() {
        // Given
        LocationFilterRequest filter = new LocationFilterRequest();
        filter.setBlastZoneId(null);
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            locationRepository.fetchLocationResponsesByBlastZoneId(filter, null, null, 0, 10);
        });
    }
    
    @Test
    void testCountLocationResponsesByBlastZoneId_WithNullFilter() {
        // Given
        LocationFilterRequest filter = new LocationFilterRequest();
        filter.setBlastZoneId(null);
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            locationRepository.countLocationResponsesByBlastZoneId(filter);
        });
    }
}
