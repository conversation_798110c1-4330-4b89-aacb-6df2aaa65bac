package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.dto.AccountFilterRequest;
import com.concirrus.zoneDataService.model.Account;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class AccountRepositoryTest {
    @Mock
    private MongoTemplate mongoTemplate;
    @InjectMocks
    private AccountRepository accountRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testFindBySubmissionIds_AllParams() {
        List<Account> expected = Arrays.asList(new Account(), new Account());
        when(mongoTemplate.find(any(Query.class), eq(Account.class))).thenReturn(expected);
        List<Account> result = accountRepository.findBySubmissionIds(Arrays.asList("id1", "id2"), "binder", 0, 2);
        assertEquals(2, result.size());
    }

    @Test
    void testFindBySubmissionIds_EmptyIds() {
        when(mongoTemplate.find(any(Query.class), eq(Account.class))).thenReturn(Collections.emptyList());
        List<Account> result = accountRepository.findBySubmissionIds(Collections.emptyList(), null, 0, 2);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindBySubmissionIds_NullIdsAndBinder() {
        when(mongoTemplate.find(any(Query.class), eq(Account.class))).thenReturn(Collections.emptyList());
        List<Account> result = accountRepository.findBySubmissionIds(null, null, 0, 2);
        assertTrue(result.isEmpty());
    }

    // Add more tests for other public methods in AccountRepository as needed
}

