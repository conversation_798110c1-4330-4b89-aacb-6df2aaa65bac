package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.dto.BlastZoneDTO;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.dto.MyMapper;
import com.concirrus.zoneDataService.model.BlastZone;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.geo.GeoJsonPolygon;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BlastZoneRepositoryTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MyMapper mapper;

    @InjectMocks
    private BlastZoneRepository blastZoneRepository;

    private BlastZone blastZone1;
    private BlastZone blastZone2;
    private BlastZoneDTO blastZoneDTO1;
    private BlastZoneDTO blastZoneDTO2;
    private final String COLLECTION_NAME = "blastZones";

    @BeforeEach
    void setUp() {
        blastZone1 = new BlastZone();
        blastZone1.setId(1L);
        blastZone1.setName("Test Zone 1");
        blastZone1.setCountry("US");

        blastZone2 = new BlastZone();
        blastZone2.setId(2L);
        blastZone2.setName("Test Zone 2");
        blastZone2.setCountry("CA");

        blastZoneDTO1 = new BlastZoneDTO();
        blastZoneDTO1.setId("1");
        blastZoneDTO1.setName("Test Zone 1");

        blastZoneDTO2 = new BlastZoneDTO();
        blastZoneDTO2.setId("2");
        blastZoneDTO2.setName("Test Zone 2");
    }

    @Test
    void testFilterBlastZones_WithAllFilters() {
        // Given
        List<Long> blastZoneIds = Arrays.asList(1L, 2L);
        List<String> countries = Arrays.asList("US", "CA");
        String sortBy = "name";
        String sortOrder = "asc";
        int page = 0;
        int size = 1;
        Pageable pageable = PageRequest.of(page, size);

        // Total items in the database is 2, we're on page 0 with size 1
        when(mongoTemplate.count(any(Query.class), eq(BlastZone.class), eq(COLLECTION_NAME)))
                .thenReturn(2L);
        when(mongoTemplate.find(any(Query.class), eq(BlastZone.class), eq(COLLECTION_NAME)))
                .thenReturn(Collections.singletonList(blastZone1));
        when(mapper.fromEntity(blastZone1)).thenReturn(blastZoneDTO1);

        // When
        CustomPageResponse<BlastZoneDTO> response = blastZoneRepository.filterBlastZones(
                blastZoneIds, countries, sortBy, sortOrder, pageable, COLLECTION_NAME);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getData().size());
        assertEquals(1, response.getPage());
        assertEquals(1, response.getSize());
        // hasMore should be true because (0 + 1) * 1 < 2
        assertTrue(response.isHasMore());
        assertEquals(2, response.getTotal());
        assertEquals(blastZoneDTO1, response.getData().get(0));
    }

    @Test
    void testSearchBlastZones() {
        // Given
        String searchText = "Test";
        int page = 0;
        int size = 1;
        Pageable pageable = PageRequest.of(page, size);

        // Total items in the database is 2, we're on page 0 with size 1
        when(mongoTemplate.count(any(Query.class), eq(BlastZone.class)))
                .thenReturn(2L);
        when(mongoTemplate.find(any(Query.class), eq(BlastZone.class)))
                .thenReturn(Collections.singletonList(blastZone1));
        when(mapper.fromEntity(blastZone1)).thenReturn(blastZoneDTO1);

        // When
        CustomPageResponse<BlastZoneDTO> response = blastZoneRepository.searchBlastZones(searchText, pageable);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getData().size());
        assertEquals(1, response.getPage());
        assertEquals(1, response.getSize());
        // hasMore should be true because (0 + 1) * 1 < 2
        assertTrue(response.isHasMore());
        assertEquals(2, response.getTotal());
    }

    @Test
    void testFindById() {
        // Given
        String id = "1";
        Query query = new Query(Criteria.where("_id").is(1L));
        query.fields().exclude("geometry");

        when(mongoTemplate.findOne(eq(query), eq(BlastZone.class))).thenReturn(blastZone1);
        when(mapper.fromEntity(blastZone1)).thenReturn(blastZoneDTO1);

        // When
        BlastZoneDTO result = blastZoneRepository.findById(id);

        // Then
        assertNotNull(result);
        assertEquals(blastZoneDTO1.getId(), result.getId());
        assertEquals(blastZoneDTO1.getName(), result.getName());
    }

    @Test
    void testFindById_InvalidId() {
        // Given
        String invalidId = "invalid-id";
        
        // When
        BlastZoneDTO result = blastZoneRepository.findById(invalidId);
        
        // Then
        assertNull(result);
        
        // Verify that mongoTemplate.findOne is never called for invalid IDs
        verify(mongoTemplate, never()).findOne(any(Query.class), eq(BlastZone.class));
    }

    @Test
    void testFindById_NumericOverflow() {
        // Given
        String overflowId = "999999999999999999999999999999999999999999999999999999999999999";
        
        // When
        BlastZoneDTO result = blastZoneRepository.findById(overflowId);
        
        // Then
        assertNull(result);
        
        // Verify that mongoTemplate.findOne is never called for invalid IDs
        verify(mongoTemplate, never()).findOne(any(Query.class), eq(BlastZone.class));
    }

    @Test
    void testFindAll() {
        // Given
        when(mongoTemplate.findAll(BlastZone.class)).thenReturn(Arrays.asList(blastZone1, blastZone2));

        // When
        List<BlastZone> result = blastZoneRepository.findAll();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(mongoTemplate).findAll(BlastZone.class);
    }

    @Test
    void testFilterBlastZones_WithNullFilters() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        when(mongoTemplate.count(any(Query.class), eq(BlastZone.class), eq(COLLECTION_NAME))).thenReturn(2L);
        when(mongoTemplate.find(any(Query.class), eq(BlastZone.class), eq(COLLECTION_NAME)))
                .thenReturn(Arrays.asList(blastZone1, blastZone2));
        when(mapper.fromEntity(any(BlastZone.class))).thenAnswer(i -> {
            BlastZone zone = i.getArgument(0);
            BlastZoneDTO dto = new BlastZoneDTO();
            dto.setId(String.valueOf(zone.getId()));
            dto.setName(zone.getName());
            return dto;
        });

        // When
        CustomPageResponse<BlastZoneDTO> response = blastZoneRepository.filterBlastZones(
                null, null, null, null, pageable, COLLECTION_NAME);

        // Then
        assertNotNull(response);
        assertEquals(2, response.getData().size());
        assertFalse(response.isHasMore());
        assertEquals(1, response.getPage());
        assertEquals(10, response.getSize());
    }

    @Test
    void testSearchBlastZonesWithMultipleResults() {
        // Given
        String searchText = "test";
        Pageable pageable = PageRequest.of(0, 10);

        when(mongoTemplate.count(any(Query.class), eq(BlastZone.class))).thenReturn(2L);
        when(mongoTemplate.find(any(Query.class), eq(BlastZone.class)))
                .thenReturn(Arrays.asList(blastZone1, blastZone2));
        when(mapper.fromEntity(any(BlastZone.class))).thenAnswer(i -> {
            BlastZone zone = i.getArgument(0);
            BlastZoneDTO dto = new BlastZoneDTO();
            dto.setId(String.valueOf(zone.getId()));
            dto.setName(zone.getName());
            return dto;
        });

        // When
        CustomPageResponse<BlastZoneDTO> response = blastZoneRepository.searchBlastZones(searchText, pageable);

        // Then
        assertNotNull(response);
        assertEquals(2, response.getData().size());
        assertFalse(response.isHasMore());
        assertEquals(1, response.getPage());
        assertEquals(10, response.getSize());
    }

    @Test
    void testFindByIdWithExistingId() {
        // Given
        String id = "1";
        when(mongoTemplate.findOne(any(Query.class), eq(BlastZone.class))).thenReturn(blastZone1);
        when(mapper.fromEntity(blastZone1)).thenReturn(blastZoneDTO1);

        // When
        BlastZoneDTO result = blastZoneRepository.findById(id);

        // Then
        assertNotNull(result);
        assertEquals(blastZoneDTO1, result);
    }

    @Test
    void testFindAllWithPaging() {
        // Given
        String sortBy = "name";
        String sortOrder = "asc";
        int page = 0;
        int size = 10;

        when(mongoTemplate.find(any(Query.class), eq(BlastZone.class)))
                .thenReturn(Arrays.asList(blastZone1, blastZone2));

        // When
        List<BlastZone> result = blastZoneRepository.findAll(sortBy, sortOrder, page, size);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test
    void testSave() {
        // Given
        BlastZone zone = new BlastZone();
        zone.setId(3L);
        zone.setName("New Zone");

        // When
        blastZoneRepository.save(zone);

        // Then
        verify(mongoTemplate).save(zone);
    }

    @Test
    void testSaveExistingZone() {
        // Given
        BlastZone zone = new BlastZone();
        zone.setId(1L);
        zone.setName("Updated Zone");

        // When
        blastZoneRepository.save(zone);

        // Then
        verify(mongoTemplate).save(zone);
    }
}
