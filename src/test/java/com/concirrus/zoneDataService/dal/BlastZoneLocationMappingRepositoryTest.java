package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.model.AccountPmlProjection;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BlastZoneLocationMappingRepositoryTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private BlastZoneLocationMappingRepository repository;

    @Mock
    private AggregationResults<AccountPmlProjection> aggregationResults;

    @BeforeEach
    void setUp() {
        // Common setup if needed
    }


    @Test
    void testGetAccountPmlTotalsByBlastZoneId_WithAllFilters() {
        // Given
        Long blastZoneId = 1L;
        String pmlZone = "Zone1";
        String geocodingGrade = "A";

        AccountPmlProjection projection = new AccountPmlProjection() {
            @Override
            public String getSubmissionId() {
                return "sub123";
            }

            @Override
            public Double getTotalPml() {
                return 1000.0;
            }

            @Override
            public Double getTotalTiv() {
                return 2000.0;
            }
        };

        when(aggregationResults.getMappedResults()).thenReturn(Collections.singletonList(projection));
        when(mongoTemplate.aggregate(any(Aggregation.class), eq("blast_zone_location_mapping"), eq(AccountPmlProjection.class)))
                .thenReturn(aggregationResults);

        // When
        List<AccountPmlProjection> results = repository.getAccountPmlTotalsByBlastZoneId(blastZoneId, pmlZone, geocodingGrade);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("sub123", results.get(0).getSubmissionId());
        assertEquals(1000.0, results.get(0).getTotalPml());
        assertEquals(2000.0, results.get(0).getTotalTiv());
    }

    @Test
    void testGetBlastZoneIdsBySubmissionIds() {
        // Given
        List<String> submissionIds = Arrays.asList("sub1", "sub2");
        
        Document doc1 = new Document("blastZoneId", 1L);
        Document doc2 = new Document("blastZoneId", 2L);
        
        when(mongoTemplate.find(any(Query.class), eq(Document.class), eq("blast_zone_location_mapping")))
                .thenReturn(Arrays.asList(doc1, doc2));

        // When
        List<Long> results = repository.getBlastZoneIdsBySubmissionIds(submissionIds);

        // Then
        assertNotNull(results);
        assertEquals(2, results.size());
        assertTrue(results.contains(1L));
        assertTrue(results.contains(2L));
    }

    @Test
    void testGetBlastZoneIdsBySubmissionIds_EmptyInput() {
        // When
        List<Long> results = repository.getBlastZoneIdsBySubmissionIds(Collections.emptyList());

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testGetAccountPmlTotalsByBlastZoneId_WithOnlyRequiredParams() {
        // Given
        Long blastZoneId = 1L;

        AccountPmlProjection projection = new AccountPmlProjection() {
            @Override
            public String getSubmissionId() {
                return "sub456";
            }

            @Override
            public Double getTotalPml() {
                return 500.0;
            }

            @Override
            public Double getTotalTiv() {
                return 1000.0;
            }
        };

        when(aggregationResults.getMappedResults()).thenReturn(Collections.singletonList(projection));
        when(mongoTemplate.aggregate(any(Aggregation.class), eq("blast_zone_location_mapping"), eq(AccountPmlProjection.class)))
                .thenReturn(aggregationResults);

        // When
        List<AccountPmlProjection> results = repository.getAccountPmlTotalsByBlastZoneId(blastZoneId, null, null);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("sub456", results.get(0).getSubmissionId());
        assertEquals(500.0, results.get(0).getTotalPml());
        assertEquals(1000.0, results.get(0).getTotalTiv());
    }
}
