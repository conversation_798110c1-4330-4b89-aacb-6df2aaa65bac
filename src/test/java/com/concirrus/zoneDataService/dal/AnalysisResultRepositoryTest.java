package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.model.AnalysisResult;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AnalysisResultRepositoryTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private AnalysisResultRepository analysisResultRepository;

    private AnalysisResult result1;
    private AnalysisResult result2;

    @BeforeEach
    void setUp() {
        result1 = new AnalysisResult();
        result1.setBlastZoneId("1");
        result1.setBlastZoneName("Test Zone 1");
        result1.setCurrentExposure(1000.0);

        result2 = new AnalysisResult();
        result2.setBlastZoneId("2");
        result2.setBlastZoneName("Test Zone 2");
        result2.setCurrentExposure(2000.0);
    }

    @Test
    void testFindAnalysisResults_WithDefaultSort() {
        // Given
        String sortBy = null;
        String sortOrder = "asc";
        int page = 0;
        int size = 10;

        when(mongoTemplate.find(any(Query.class), eq(AnalysisResult.class)))
                .thenReturn(Arrays.asList(result1, result2));

        // When
        List<AnalysisResult> results = analysisResultRepository.findAnalysisResults(sortBy, sortOrder, page, size);

        // Then
        assertNotNull(results);
        assertEquals(2, results.size());
        
        // Verify the query
        ArgumentCaptor<Query> queryCaptor = ArgumentCaptor.forClass(Query.class);
        verify(mongoTemplate).find(queryCaptor.capture(), eq(AnalysisResult.class));
        
        Query capturedQuery = queryCaptor.getValue();
        assertNotNull(capturedQuery);
        assertEquals(0, capturedQuery.getSkip());
        assertEquals(10, capturedQuery.getLimit());
    }

    @Test
    void testFindAnalysisResults_WithCustomSortAndPagination() {
        // Given
        String sortBy = "currentExposure";
        String sortOrder = "desc";
        int page = 1;
        int size = 1;

        when(mongoTemplate.find(any(Query.class), eq(AnalysisResult.class)))
                .thenReturn(List.of(result2)); // Second page with one item

        // When
        List<AnalysisResult> results = analysisResultRepository.findAnalysisResults(sortBy, sortOrder, page, size);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(result2.getBlastZoneId(), results.get(0).getBlastZoneId());
        
        // Verify the query
        ArgumentCaptor<Query> queryCaptor = ArgumentCaptor.forClass(Query.class);
        verify(mongoTemplate).find(queryCaptor.capture(), eq(AnalysisResult.class));
        
        Query capturedQuery = queryCaptor.getValue();
        assertNotNull(capturedQuery);
        assertEquals(1, capturedQuery.getSkip());
        assertEquals(1, capturedQuery.getLimit());
        
        // Verify sort
        assertTrue(capturedQuery.getSortObject().containsKey("currentExposure"));
        assertEquals(-1, capturedQuery.getSortObject().get("currentExposure"));
    }
    
    @Test
    void testFindAnalysisResults_WithInvalidSortOrder() {
        // Given
        String sortBy = "currentExposure";
        String sortOrder = "invalid"; // Will default to ascending
        int page = 0;
        int size = 10;

        when(mongoTemplate.find(any(Query.class), eq(AnalysisResult.class)))
                .thenReturn(Arrays.asList(result1, result2));

        // When
        List<AnalysisResult> results = analysisResultRepository.findAnalysisResults(sortBy, sortOrder, page, size);

        // Then
        assertNotNull(results);
        assertEquals(2, results.size());
        
        // Verify the query
        ArgumentCaptor<Query> queryCaptor = ArgumentCaptor.forClass(Query.class);
        verify(mongoTemplate).find(queryCaptor.capture(), eq(AnalysisResult.class));
        
        Query capturedQuery = queryCaptor.getValue();
        assertNotNull(capturedQuery);
        
        // Should default to ascending sort
        assertTrue(capturedQuery.getSortObject().containsKey("currentExposure"));
        assertEquals(1, capturedQuery.getSortObject().get("currentExposure"));
    }
    
    @Test
    void testFindAnalysisResults_WithEmptyResult() {
        // Given
        String sortBy = "currentExposure";
        String sortOrder = "asc";
        int page = 0;
        int size = 10;

        when(mongoTemplate.find(any(Query.class), eq(AnalysisResult.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<AnalysisResult> results = analysisResultRepository.findAnalysisResults(sortBy, sortOrder, page, size);

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }
    
    @Test
    void testFindAnalysisResults_WithNullParameters() {
        // Given
        when(mongoTemplate.find(any(Query.class), eq(AnalysisResult.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<AnalysisResult> results = analysisResultRepository.findAnalysisResults(null, null, 0, 0);

        // Then
        assertNotNull(results);
        
        // Verify default pagination was used (0, 0) which means no pagination
        ArgumentCaptor<Query> queryCaptor = ArgumentCaptor.forClass(Query.class);
        verify(mongoTemplate).find(queryCaptor.capture(), eq(AnalysisResult.class));
        
        Query capturedQuery = queryCaptor.getValue();
        assertNotNull(capturedQuery);
        assertEquals(0, capturedQuery.getSkip());
        assertEquals(0, capturedQuery.getLimit());
    }
}
