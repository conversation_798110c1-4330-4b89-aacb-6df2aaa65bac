package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.model.BlastZoneLocationMapping;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class BlastZoneLocationMappingRepositoryTest {
    @Mock
    private MongoTemplate mongoTemplate;
    @InjectMocks
    private BlastZoneLocationMappingRepository repo;

    @BeforeEach
    void setUp() { MockitoAnnotations.openMocks(this); }

    @Test
    void testFindBySubmissionId() {
        List<BlastZoneLocationMapping> expected = Collections.singletonList(new BlastZoneLocationMapping());
        when(mongoTemplate.find(any(), eq(BlastZoneLocationMapping.class))).thenReturn(expected);
        List<BlastZoneLocationMapping> result = repo.findBySubmissionId("sub1");
        assertEquals(1, result.size());
    }

    @Test
    void testFindBySubmissionId_Empty() {
        when(mongoTemplate.find(any(), eq(BlastZoneLocationMapping.class))).thenReturn(Collections.emptyList());
        List<BlastZoneLocationMapping> result = repo.findBySubmissionId("sub2");
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindBySubmissionId_Null() {
        when(mongoTemplate.find(any(), eq(BlastZoneLocationMapping.class))).thenReturn(null);
        List<BlastZoneLocationMapping> result = repo.findBySubmissionId("sub3");
        assertNull(result);
    }

    @Test
    void testFindBySubmissionId_EmptyString() {
        when(mongoTemplate.find(any(), eq(BlastZoneLocationMapping.class))).thenReturn(Collections.emptyList());
        List<BlastZoneLocationMapping> result = repo.findBySubmissionId("");
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindBySubmissionId_NullString() {
        when(mongoTemplate.find(any(), eq(BlastZoneLocationMapping.class))).thenReturn(Collections.emptyList());
        List<BlastZoneLocationMapping> result = repo.findBySubmissionId(null);
        assertTrue(result.isEmpty());
    }
}
