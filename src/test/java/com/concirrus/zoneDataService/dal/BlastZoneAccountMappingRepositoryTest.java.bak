package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.model.BlastZoneAccountMapping;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class BlastZoneAccountMappingRepositoryTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private BlastZoneAccountMappingRepository repository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testFindByBlastZoneIds() {
        // Given
        List<Long> blastZoneIds = Arrays.asList(1L, 2L, 3L);
        BlastZoneAccountMapping mapping1 = BlastZoneAccountMapping.builder()
                .blastZoneId(1L)
                .accountId("acc1")
                .blastZoneTiv(100.0)
                .blastZoneExposure(200.0)
                .blastZonePml(300.0)
                .build();
        
        BlastZoneAccountMapping mapping2 = BlastZoneAccountMapping.builder()
                .blastZoneId(2L)
                .accountId("acc2")
                .blastZoneTiv(200.0)
                .blastZoneExposure(300.0)
                .blastZonePml(400.0)
                .build();
        
        when(mongoTemplate.find(any(Query.class), eq(BlastZoneAccountMapping.class)))
                .thenReturn(Arrays.asList(mapping1, mapping2));

        // When
        List<BlastZoneAccountMapping> result = repository.findByBlastZoneIds(blastZoneIds);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(mongoTemplate).find(any(Query.class), eq(BlastZoneAccountMapping.class));
    }

    @Test
    void testFindByBlastZoneIds_EmptyList() {
        // Given
        when(mongoTemplate.find(any(Query.class), eq(BlastZoneAccountMapping.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<BlastZoneAccountMapping> result = repository.findByBlastZoneIds(Collections.singletonList(1L));

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mongoTemplate).find(any(Query.class), eq(BlastZoneAccountMapping.class));
    }

    @Test
    void testFindByBlastZoneIds_NullInput() {
        // When
        List<BlastZoneAccountMapping> result = repository.findByBlastZoneIds(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mongoTemplate, never()).find(any(Query.class), eq(BlastZoneAccountMapping.class));
    }

    @Test
    void testFindByAccountIds() {
        // Given
        List<String> accountIds = Arrays.asList("acc1", "acc2");
        BlastZoneAccountMapping mapping = BlastZoneAccountMapping.builder()
                .blastZoneId(1L)
                .accountId("acc1")
                .blastZoneTiv(100.0)
                .blastZoneExposure(200.0)
                .blastZonePml(300.0)
                .build();
        
        when(mongoTemplate.find(any(Query.class), eq(BlastZoneAccountMapping.class)))
                .thenReturn(Collections.singletonList(mapping));

        // When
        List<BlastZoneAccountMapping> result = repository.findByAccountIds(accountIds);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("acc1", result.get(0).getAccountId());
        verify(mongoTemplate).find(any(Query.class), eq(BlastZoneAccountMapping.class));
    }

    @Test
    void testFindByAccountIds_EmptyList() {
        // Given
        when(mongoTemplate.find(any(Query.class), eq(BlastZoneAccountMapping.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<BlastZoneAccountMapping> result = repository.findByAccountIds(Collections.singletonList("nonexistent"));

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mongoTemplate).find(any(Query.class), eq(BlastZoneAccountMapping.class));
    }

    @Test
    void testFindByAccountIds_NullInput() {
        // When
        List<BlastZoneAccountMapping> result = repository.findByAccountIds(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mongoTemplate, never()).find(any(Query.class), eq(BlastZoneAccountMapping.class));
    }
}
