package com.concirrus.zoneDataService.sal;

import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MiAnalysisSalTest {

    @Mock
    private RestTemplate restTemplate;

    private MiAnalysisSal miAnalysisSal;

    @BeforeEach
    void setUp() {
        miAnalysisSal = new MiAnalysisSal(restTemplate, "test-namespace");
    }

    @Test
    void testGetMiAnalysisJob_Success() {
        // Arrange
        String clientId = "test-client";
        String submissionId = "sub123";
        String quoteId = "quote456";
        
        Map<String, String> expectedResult = Map.of("jobId", "job123", "status", "CREATED");
        BasicResponse<Map<String, String>> responseBody = BasicResponse.result(expectedResult);
        ResponseEntity<BasicResponse<Map<String, String>>> response = 
                new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(response);

        // Act
        Map<String, String> result = miAnalysisSal.getMiAnalysisJob(clientId, submissionId, quoteId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(restTemplate).exchange(
                anyString(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        );
    }

    @Test
    void testGetMiAnalysisJob_InvalidClientId() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.getMiAnalysisJob(null, "sub123", "quote456"));
        
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.getMiAnalysisJob("", "sub123", "quote456"));
        
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.getMiAnalysisJob("   ", "sub123", "quote456"));
    }

    @Test
    void testGetMiAnalysisJob_InvalidSubmissionId() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.getMiAnalysisJob("client123", null, "quote456"));
        
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.getMiAnalysisJob("client123", "", "quote456"));
    }

    @Test
    void testGetMiAnalysisJob_InvalidQuoteId() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.getMiAnalysisJob("client123", "sub123", null));
        
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.getMiAnalysisJob("client123", "sub123", ""));
    }

    @Test
    void testCreateMiAnalysisJob_Success() {
        // Arrange
        String clientId = "test-client";
        MiAnalysisJobRequest request = new MiAnalysisJobRequest();
        request.setSubmissionId("sub123");
        request.setQuoteId("quote456");
        
        com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob expectedResult = 
                new com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob();
        expectedResult.setJobId("job123");
        
        BasicResponse<com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob> responseBody = 
                BasicResponse.result(expectedResult);
        ResponseEntity<BasicResponse<com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob>> response = 
                new ResponseEntity<>(responseBody, HttpStatus.CREATED);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(response);

        // Act
        com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob result = 
                miAnalysisSal.createMiAnalysisJob(clientId, request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getJobId(), result.getJobId());
        verify(restTemplate).exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        );
    }

    @Test
    void testCreateMiAnalysisJob_InvalidRequest() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.createMiAnalysisJob("client123", null));
    }

    @Test
    void testExecuteMiAnalysisJob_Success() {
        // Arrange
        String clientId = "test-client";
        String jobId = "job123";
        String expectedResult = "JOB Started for Job_Id:job123";
        
        BasicResponse<String> responseBody = BasicResponse.result(expectedResult);
        ResponseEntity<BasicResponse<String>> response = 
                new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(response);

        // Act
        String result = miAnalysisSal.executeMiAnalysisJob(clientId, jobId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(restTemplate).exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        );
    }

    @Test
    void testExecuteMiAnalysisJob_InvalidJobId() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.executeMiAnalysisJob("client123", null));
        
        assertThrows(IllegalArgumentException.class, () -> 
                miAnalysisSal.executeMiAnalysisJob("client123", ""));
    }

    @Test
    void testDeleteMiAnalysisJob_Success() {
        // Arrange
        String clientId = "test-client";
        String submissionId = "sub123";
        String quoteId = "quote456";
        String expectedResult = "Deleted MI Analysis job successfully";
        
        BasicResponse<String> responseBody = BasicResponse.result(expectedResult);
        ResponseEntity<BasicResponse<String>> response = 
                new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.DELETE),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(response);

        // Act
        String result = miAnalysisSal.deleteMiAnalysisJob(clientId, submissionId, quoteId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(restTemplate).exchange(
                anyString(),
                eq(HttpMethod.DELETE),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        );
    }
}
