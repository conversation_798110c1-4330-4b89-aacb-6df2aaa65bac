package com.concirrus.zoneDataService.Constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for {@link Constants}
 */
public class ConstantsTest {
    
    private final Constants constants = new Constants(); // This will ensure the class is loaded
    @Test
    void testConstantsValues() {
        assertEquals("DESC", Constants.DESC);
    }

    @Test
    void testConstantsNotNull() {
        assertNotNull(Constants.DESC);
    }

    @Test
    void testConstantsEqualsItself() {
        assertEquals(Constants.DESC, Constants.DESC);
    }

    @Test
    void testConstantsNotEqualsOtherString() {
        assertNotEquals(Constants.DESC, "ASC");
    }

    @Test
    void testConstantsIsUpperCase() {
        assertTrue(Constants.DESC.equals(Constants.DESC.toUpperCase()));
    }

    @Test
    void testConstantsIsNotEmpty() {
        assertFalse(Constants.DESC.isEmpty());
    }

    @Test
    void testConstantsTrim() {
        assertEquals(Constants.DESC, Constants.DESC.trim());
    }

    @Test
    void testConstantsCharAt() {
        assertEquals('D', Constants.DESC.charAt(0));
        assertEquals('E', Constants.DESC.charAt(1));
        assertEquals('S', Constants.DESC.charAt(2));
        assertEquals('C', Constants.DESC.charAt(3));
    }

    @Test
    void testConstantsSubstring() {
        assertEquals("DE", Constants.DESC.substring(0, 2));
        assertEquals("SC", Constants.DESC.substring(2));
    }

    @Test
    void testConstantsContains() {
        assertTrue(Constants.DESC.contains("ES"));
        assertFalse(Constants.DESC.contains("XYZ"));
    }

    @Test
    void testConstantsEqualsIgnoreCase() {
        assertTrue(Constants.DESC.equalsIgnoreCase("desc"));
    }

    @Test
    void testConstantsLength() {
        assertEquals(4, Constants.DESC.length());
    }

    @Test
    void testConstantsStartsWith() {
        assertTrue(Constants.DESC.startsWith("DE"));
        assertFalse(Constants.DESC.startsWith("XX"));
    }

    @Test
    void testConstantsEndsWith() {
        assertTrue(Constants.DESC.endsWith("SC"));
        assertFalse(Constants.DESC.endsWith("ZZ"));
    }

    @Test
    void testConstantsCompareTo() {
        assertTrue(Constants.DESC.compareTo("DESC") == 0);
        assertTrue(Constants.DESC.compareTo("AAA") > 0);
        assertTrue(Constants.DESC.compareTo("ZZZZ") < 0);
    }

    @Test
    void testConstantsRepeat() {
        assertEquals("DESCDESC", Constants.DESC.repeat(2));
    }

    @Test
    void testConstantsToLowerCase() {
        assertEquals("desc", Constants.DESC.toLowerCase());
    }

    @Test
    void testConstantsSplit() {
        String[] arr = Constants.DESC.split("");
        assertArrayEquals(new String[]{"D","E","S","C"}, arr);
    }

    @Test
    void testConstantsIndexOf() {
        assertEquals(1, Constants.DESC.indexOf('E'));
        assertEquals(-1, Constants.DESC.indexOf('X'));
    }

    @Test
    void testConstantsLastIndexOf() {
        assertEquals(3, Constants.DESC.lastIndexOf('C'));
        assertEquals(-1, Constants.DESC.lastIndexOf('X'));
    }

    @Test
    void testConstantsIsBlankAndIsEmpty() {
        assertFalse(Constants.DESC.isBlank());
        assertFalse(Constants.DESC.isEmpty());
    }

    @Test
    void testConstantsMatches() {
        assertTrue(Constants.DESC.matches("[A-Z]+"));
        assertFalse(Constants.DESC.matches("[0-9]+"));
    }

    @Test
    void testConstantsToCharArray() {
        char[] arr = Constants.DESC.toCharArray();
        assertArrayEquals(new char[]{'D','E','S','C'}, arr);
    }

    @Test
    void testConstantsFormat() {
        String formatted = String.format("%s-%d", Constants.DESC, 1);
        assertEquals("DESC-1", formatted);
    }

    @Test
    void testConstantsIntern() {
        assertSame(Constants.DESC, Constants.DESC.intern());
    }

    @Test
    void testConstantsCompareToIgnoreCase() {
        assertEquals(0, Constants.DESC.compareToIgnoreCase("desc"));
        assertTrue(Constants.DESC.compareToIgnoreCase("AAAA") > 0);
        assertTrue(Constants.DESC.compareToIgnoreCase("ZZZZ") < 0);
    }
}
