package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;

class CustomPageResponseTest {
    @Test
    void testAllArgsConstructorAndGetters() {
        List<String> data = Arrays.asList("a", "b");
        CustomPageResponse<String> resp = new CustomPageResponse<>(10L, true, 2, 5, data);
        assertEquals(10L, resp.getTotal());
        assertTrue(resp.isHasMore());
        assertEquals(2, resp.getPage());
        assertEquals(5, resp.getSize());
        assertEquals(data, resp.getData());
    }

    @Test
    void testBuilder() {
        List<Integer> data = Collections.singletonList(42);
        CustomPageResponse<Integer> resp = CustomPageResponse.<Integer>builder()
                .total(5L)
                .hasMore(false)
                .page(1)
                .size(10)
                .data(data)
                .build();
        assertEquals(5L, resp.getTotal());
        assertFalse(resp.isHasMore());
        assertEquals(1, resp.getPage());
        assertEquals(10, resp.getSize());
        assertEquals(data, resp.getData());
    }

    @Test
    void testEqualsAndHashCode() {
        List<String> data = Arrays.asList("x", "y");
        CustomPageResponse<String> r1 = new CustomPageResponse<>(1L, false, 0, 2, data);
        CustomPageResponse<String> r2 = new CustomPageResponse<>(1L, false, 0, 2, data);
        CustomPageResponse<String> r3 = new CustomPageResponse<>(2L, true, 1, 3, Collections.singletonList("z"));
        assertEquals(r1, r2);
        assertEquals(r1.hashCode(), r2.hashCode());
        assertNotEquals(r1, r3);
    }
}

