package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class LocationResponseTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        LocationResponse resp = new LocationResponse();
        resp.setPolicyReference("ref");
        resp.setLocationName("loc");
        resp.setStreet("street");
        resp.setCity("city");
        resp.setLatitude(1.1);
        resp.setLongitude(2.2);
        resp.setPostCode("pc");
        resp.setGeocoding("geo");
        resp.setTiv(3.3);
        resp.setPmlZone("zone");
        resp.setPmlPercentage(4.4);
        resp.setPmlTiv(5.5);
        assertEquals("ref", resp.getPolicyReference());
        assertEquals("loc", resp.getLocationName());
        assertEquals("street", resp.getStreet());
        assertEquals("city", resp.getCity());
        assertEquals(1.1, resp.getLatitude());
        assertEquals(2.2, resp.getLongitude());
        assertEquals("pc", resp.getPostCode());
        assertEquals("geo", resp.getGeocoding());
        assertEquals(3.3, resp.getTiv());
        assertEquals("zone", resp.getPmlZone());
        assertEquals(4.4, resp.getPmlPercentage());
        assertEquals(5.5, resp.getPmlTiv());
    }

    @Test
    void testEqualsAndHashCode() {
        LocationResponse r1 = new LocationResponse();
        r1.setPolicyReference("a");
        r1.setLocationName("b");
        r1.setStreet("c");
        r1.setCity("d");
        r1.setLatitude(1.0);
        r1.setLongitude(2.0);
        r1.setPostCode("e");
        r1.setGeocoding("f");
        r1.setTiv(3.0);
        r1.setPmlZone("g");
        r1.setPmlPercentage(4.0);
        r1.setPmlTiv(5.0);
        LocationResponse r2 = new LocationResponse();
        r2.setPolicyReference("a");
        r2.setLocationName("b");
        r2.setStreet("c");
        r2.setCity("d");
        r2.setLatitude(1.0);
        r2.setLongitude(2.0);
        r2.setPostCode("e");
        r2.setGeocoding("f");
        r2.setTiv(3.0);
        r2.setPmlZone("g");
        r2.setPmlPercentage(4.0);
        r2.setPmlTiv(5.0);
        assertEquals(r1, r2);
        assertEquals(r1.hashCode(), r2.hashCode());
        r2.setPmlTiv(9.0);
        assertNotEquals(r1, r2);
    }

    @Test
    void testToStringAndNulls() {
        LocationResponse resp = new LocationResponse();
        assertNotNull(resp.toString());
        resp.setPolicyReference(null);
        resp.setLocationName(null);
        resp.setStreet(null);
        resp.setCity(null);
        resp.setLatitude(null);
        resp.setLongitude(null);
        resp.setPostCode(null);
        resp.setGeocoding(null);
        resp.setTiv(null);
        resp.setPmlZone(null);
        resp.setPmlPercentage(null);
        resp.setPmlTiv(null);
        assertNotNull(resp.toString());
    }
}
