package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class AnalysisResultDTOTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        AnalysisResultDTO dto = new AnalysisResultDTO();
        dto.setBlastZoneId("zone1");
        dto.setBlastZoneName("ZoneName");
        dto.setCurrentExposure(1.1);
        dto.setCurrentExposureUtilisation(2.2);
        dto.setCurrentPmlContribution(3.3);
        dto.setCurrentPmlUtilisation(4.4);
        dto.setUpdatedExposure(5.5);
        dto.setUpdatedExposureUtilisation(6.6);
        dto.setAccountContribution(7.7);
        dto.setUpdatedPmlExposure(8.8);
        assertEquals("zone1", dto.getBlastZoneId());
        assertEquals("ZoneName", dto.getBlastZoneName());
        assertEquals(1.1, dto.getCurrentExposure());
        assertEquals(2.2, dto.getCurrentExposureUtilisation());
        assertEquals(3.3, dto.getCurrentPmlContribution());
        assertEquals(4.4, dto.getCurrentPmlUtilisation());
        assertEquals(5.5, dto.getUpdatedExposure());
        assertEquals(6.6, dto.getUpdatedExposureUtilisation());
        assertEquals(7.7, dto.getAccountContribution());
        assertEquals(8.8, dto.getUpdatedPmlExposure());
    }

    @Test
    void testAllArgsConstructor() {
        AnalysisResultDTO dto = new AnalysisResultDTO("id","name",1.0,2.0,3.0,4.0,5.0,6.0,7.0,8.0);
        assertEquals("id", dto.getBlastZoneId());
        assertEquals("name", dto.getBlastZoneName());
        assertEquals(1.0, dto.getCurrentExposure());
        assertEquals(2.0, dto.getCurrentExposureUtilisation());
        assertEquals(3.0, dto.getCurrentPmlContribution());
        assertEquals(4.0, dto.getCurrentPmlUtilisation());
        assertEquals(5.0, dto.getUpdatedExposure());
        assertEquals(6.0, dto.getUpdatedExposureUtilisation());
        assertEquals(7.0, dto.getAccountContribution());
        assertEquals(8.0, dto.getUpdatedPmlExposure());
    }

    @Test
    void testEqualsAndHashCode() {
        AnalysisResultDTO dto1 = new AnalysisResultDTO("a","b",1.0,2.0,3.0,4.0,5.0,6.0,7.0,8.0);
        AnalysisResultDTO dto2 = new AnalysisResultDTO("a","b",1.0,2.0,3.0,4.0,5.0,6.0,7.0,8.0);
        AnalysisResultDTO dto3 = new AnalysisResultDTO("x","y",9.0,8.0,7.0,6.0,5.0,4.0,3.0,2.0);
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
    }
}

