package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class AccountFilterRequestTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        AccountFilterRequest req = new AccountFilterRequest();
        req.setBlastZoneId("zone123");
        req.setGeoCoding("geo");
        req.setBinder("binder1");
        req.setPml("pml1");
        assertEquals("zone123", req.getBlastZoneId());
        assertEquals("geo", req.getGeoCoding());
        assertEquals("binder1", req.getBinder());
        assertEquals("pml1", req.getPml());
    }

    @Test
    void testEqualsAndHashCode() {
        AccountFilterRequest req1 = new AccountFilterRequest();
        req1.setBlastZoneId("z");
        req1.setGeoCoding("g");
        req1.setBinder("b");
        req1.setPml("p");
        AccountFilterRequest req2 = new AccountFilterRequest();
        req2.setBlastZoneId("z");
        req2.setGeoCoding("g");
        req2.setBinder("b");
        req2.setPml("p");
        assertEquals(req1, req2);
        assertEquals(req1.hashCode(), req2.hashCode());
        req2.setPml("diff");
        assertNotEquals(req1, req2);
    }
}

