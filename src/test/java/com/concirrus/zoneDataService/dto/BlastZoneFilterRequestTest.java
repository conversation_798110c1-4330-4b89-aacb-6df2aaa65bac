package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;

class BlastZoneFilterRequestTest {
    @Test
    void testSettersAndGetters() {
        BlastZoneFilterRequest req = new BlastZoneFilterRequest();
        List<String> accounts = Arrays.asList("a1", "a2");
        List<String> countries = Collections.singletonList("UK");
        LocalDate date = LocalDate.of(2024, 6, 26);
        req.setAccounts(accounts);
        req.setCountry(countries);
        req.setRollUpDate(date);
        assertEquals(accounts, req.getAccounts());
        assertEquals(countries, req.getCountry());
        assertEquals(date, req.getRollUpDate());
    }

    @Test
    void testToString() {
        BlastZoneFilterRequest req = new BlastZoneFilterRequest();
        req.setAccounts(Arrays.asList("x", "y"));
        req.setCountry(Arrays.asList("IN", "US"));
        req.setRollUpDate(LocalDate.of(2025, 1, 1));
        String str = req.toString();
        assertTrue(str.contains("accounts=[x, y]"));
        assertTrue(str.contains("country=[IN, US]"));
        assertTrue(str.contains("rollUpDate=2025-01-01"));
    }

    @Test
    void testToStringWithNulls() {
        BlastZoneFilterRequest req = new BlastZoneFilterRequest();
        String str = req.toString();
        assertTrue(str.contains("accounts=null"));
        assertTrue(str.contains("country=null"));
        assertTrue(str.contains("rollUpDate=null"));
    }
}
