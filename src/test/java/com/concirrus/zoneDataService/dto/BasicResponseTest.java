package com.concirrus.zoneDataService.dto;

import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import java.time.Instant;
import static org.junit.jupiter.api.Assertions.*;

class BasicResponseTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        BasicResponse<String> response = new BasicResponse<>();
        response.setStatus(200);
        response.setResult("OK");
        response.setError("None");
        assertEquals(200, response.getStatus());
        assertEquals("OK", response.getResult());
        assertEquals("None", response.getError());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testResultStaticFactory() {
        BasicResponse<String> response = BasicResponse.result("Success");
        assertEquals("Success", response.getResult());
        assertNull(response.getError());
    }

    @Test
    void testErrorStaticFactory() {
        BasicResponse<Object> response = BasicResponse.error("SomeError");
        assertEquals("SomeError", response.getError());
        assertNull(response.getResult());
    }

    @Test
    void testStatusStaticFactory() {
        BasicResponse<Object> response = BasicResponse.status(HttpStatus.NOT_FOUND);
        assertEquals(HttpStatus.NOT_FOUND.value(), response.getStatus());
    }

    @Test
    void testFluentSetters() {
        BasicResponse<String> response = new BasicResponse<>();
        response.setStatus(201).setResult("Created").setError(null);
        assertEquals(201, response.getStatus());
        assertEquals("Created", response.getResult());
        assertNull(response.getError());
    }

    @Test
    void testTimestampIsSetOnCreation() {
        BasicResponse<String> response1 = new BasicResponse<>();
        BasicResponse<String> response2 = new BasicResponse<>();
        Instant t1 = response1.getTimestamp();
        Instant t2 = response2.getTimestamp();
        assertNotNull(t1);
        assertNotNull(t2);
        assertTrue(!t1.isAfter(t2) || !t2.isAfter(t1)); // Should be very close
    }
}

