package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class LocationFilterRequestTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        LocationFilterRequest req = new LocationFilterRequest();
        req.setAccountId("acc");
        req.setSubmissionId("sub");
        req.setBlastZoneId("zone");
        req.setGeocoding("geo");
        req.setPmlZone("pml");
        assertEquals("acc", req.getAccountId());
        assertEquals("sub", req.getSubmissionId());
        assertEquals("zone", req.getBlastZoneId());
        assertEquals("geo", req.getGeocoding());
        assertEquals("pml", req.getPmlZone());
    }

    @Test
    void testEqualsAndHashCode() {
        LocationFilterRequest r1 = new LocationFilterRequest();
        r1.setAccountId("a");
        r1.setSubmissionId("b");
        r1.setBlastZoneId("c");
        r1.setGeocoding("d");
        r1.setPmlZone("e");
        LocationFilterRequest r2 = new LocationFilterRequest();
        r2.setAccountId("a");
        r2.setSubmissionId("b");
        r2.setBlastZoneId("c");
        r2.setGeocoding("d");
        r2.setPmlZone("e");
        assertEquals(r1, r2);
        assertEquals(r1.hashCode(), r2.hashCode());
        r2.setPmlZone("diff");
        assertNotEquals(r1, r2);
    }
}

