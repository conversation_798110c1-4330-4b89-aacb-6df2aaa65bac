package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class BlastZoneDTOTest {

    @Test
    void testNoArgsConstructorAndSetters() {
        BlastZoneDTO dto = new BlastZoneDTO();
        dto.setId("id1");
        dto.setName("Test Zone");
        dto.setCountry("Country");
        dto.setCentre(new GeoJsonPoint(1.234567, 2.345678));
        dto.setTiv(100.0);
        dto.setPml(200.0);
        dto.setExposure(300.0);
        dto.setGeocodingResolution(0.5);
        dto.setGeocoding("Geo");
        dto.setPmlUtilisation(0.7);
        dto.setDfUtilisation(0.8);
        dto.setFirstExpiry("2025-01-01");
        dto.setUpdatedOn(Instant.now());
        RiskDataDTO riskData = new RiskDataDTO();
        dto.setRiskData(riskData);
        assertEquals("id1", dto.getId());
        assertEquals("Test Zone", dto.getName());
        assertEquals("Country", dto.getCountry());
        assertNotNull(dto.getCentre());
        assertEquals(100.0, dto.getTiv());
        assertEquals(200.0, dto.getPml());
        assertEquals(300.0, dto.getExposure());
        assertEquals(0.5, dto.getGeocodingResolution());
        assertEquals("Geo", dto.getGeocoding());
        assertEquals(0.7, dto.getPmlUtilisation());
        assertEquals(0.8, dto.getDfUtilisation());
        assertEquals("2025-01-01", dto.getFirstExpiry());
        assertNotNull(dto.getUpdatedOn());
        assertEquals(riskData, dto.getRiskData());
    }

    @Test
    void testAllArgsConstructor() {
        GeoJsonPoint centre = new GeoJsonPoint(1.234567, 2.345678);
        RiskDataDTO riskData = new RiskDataDTO();
        BlastZoneDTO dto = new BlastZoneDTO("id2", "Zone2", "Country2", centre, 10.0, 20.0, 30.0, 0.1, "Geo2", 0.2, 0.3, "2026-01-01", Instant.now(), riskData,20.0, 0.5);
        assertEquals("id2", dto.getId());
        assertEquals("Zone2", dto.getName());
        assertEquals("Country2", dto.getCountry());
        assertEquals(centre, dto.getCentre());
        assertEquals(10.0, dto.getTiv());
        assertEquals(20.0, dto.getPml());
        assertEquals(30.0, dto.getExposure());
        assertEquals(0.1, dto.getGeocodingResolution());
        assertEquals("Geo2", dto.getGeocoding());
        assertEquals(0.2, dto.getPmlUtilisation());
        assertEquals(0.3, dto.getDfUtilisation());
        assertEquals(20.0, dto.getBinderExposure());
        assertEquals(0.5, dto.getBinderDfUtilisation());
        assertEquals("2026-01-01", dto.getFirstExpiry());
        assertNotNull(dto.getUpdatedOn());
        assertEquals(riskData, dto.getRiskData());
    }

    @Test
    void testBuilder() {
        GeoJsonPoint centre = new GeoJsonPoint(3.14159, 2.71828);
        RiskDataDTO riskData = new RiskDataDTO();
        BlastZoneDTO dto = BlastZoneDTO.builder()
                .id("id3")
                .name("Zone3")
                .country("Country3")
                .centre(centre)
                .tiv(1.0)
                .pml(2.0)
                .exposure(3.0)
                .geocodingResolution(0.9)
                .geocoding("Geo3")
                .pmlUtilisation(0.4)
                .dfUtilisation(0.5)
                .firstExpiry("2027-01-01")
                .updatedOn(Instant.now())
                .riskData(riskData)
                .binderExposure(30.0)
                .binderDfUtilisation(0.5)
                .build();
        assertEquals("id3", dto.getId());
        assertEquals("Zone3", dto.getName());
        assertEquals("Country3", dto.getCountry());
        assertEquals(centre, dto.getCentre());
        assertEquals(1.0, dto.getTiv());
        assertEquals(2.0, dto.getPml());
        assertEquals(3.0, dto.getExposure());
        assertEquals(0.9, dto.getGeocodingResolution());
        assertEquals("Geo3", dto.getGeocoding());
        assertEquals(0.4, dto.getPmlUtilisation());
        assertEquals(0.5, dto.getDfUtilisation());
        assertEquals("2027-01-01", dto.getFirstExpiry());
        assertEquals(30.0, dto.getBinderExposure());
        assertEquals(0.5, dto.getBinderDfUtilisation());
        assertNotNull(dto.getUpdatedOn());
        assertEquals(riskData, dto.getRiskData());
    }

    @Test
    void testGetFormattedCentreWithNull() {
        BlastZoneDTO dto = new BlastZoneDTO();
        dto.setCentre(null);
        assertNull(dto.getFormattedCentre());
    }

    @Test
    void testGetFormattedCentreWithValue() {
        GeoJsonPoint centre = new GeoJsonPoint(12.3456789, 98.7654321);
        BlastZoneDTO dto = new BlastZoneDTO();
        dto.setCentre(centre);
        Map<String, Object> geoJson = dto.getFormattedCentre();
        assertNotNull(geoJson);
        assertEquals("Point", geoJson.get("type"));
        assertEquals(BigDecimal.valueOf(12.3456789), geoJson.get("x"));
        assertEquals(BigDecimal.valueOf(98.7654321), geoJson.get("y"));
        List<?> coordinates = (List<?>) geoJson.get("coordinates");
        assertEquals(2, coordinates.size());
        assertEquals(new BigDecimal("12.34568"), coordinates.get(0));
        assertEquals(new BigDecimal("98.76543"), coordinates.get(1));
    }
}

