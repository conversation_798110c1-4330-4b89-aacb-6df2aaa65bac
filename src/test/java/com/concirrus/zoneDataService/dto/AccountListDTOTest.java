package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class AccountListDTOTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        AccountListDTO dto = new AccountListDTO();
        dto.setAccountName("TestAccount");
        dto.setAccountId("12345");
        assertEquals("TestAccount", dto.getAccountName());
        assertEquals("12345", dto.getAccountId());
    }

    @Test
    void testEqualsAndHashCode() {
        AccountListDTO dto1 = new AccountListDTO();
        dto1.setAccountName("A");
        dto1.setAccountId("B");
        AccountListDTO dto2 = new AccountListDTO();
        dto2.setAccountName("A");
        dto2.setAccountId("B");
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        dto2.setAccountId("C");
        assertNotEquals(dto1, dto2);
    }
}

