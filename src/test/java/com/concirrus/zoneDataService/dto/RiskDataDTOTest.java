package com.concirrus.zoneDataService.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class RiskDataDTOTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        RiskDataDTO dto = new RiskDataDTO();
        dto.setWar(1.1);
        dto.setTerrorism(2.2);
        dto.setCivilUnrest(3.3);
        dto.setCombined(4.4);
        assertEquals(1.1, dto.getWar());
        assertEquals(2.2, dto.getTerrorism());
        assertEquals(3.3, dto.getCivilUnrest());
        assertEquals(4.4, dto.getCombined());
    }

    @Test
    void testAllArgsConstructor() {
        RiskDataDTO dto = new RiskDataDTO(5.5, 6.6, 7.7, 8.8);
        assertEquals(5.5, dto.getWar());
        assertEquals(6.6, dto.getTerrorism());
        assertEquals(7.7, dto.getCivilUnrest());
        assertEquals(8.8, dto.getCombined());
    }

    @Test
    void testEqualsAndHashCode() {
        RiskDataDTO dto1 = new RiskDataDTO(1.0, 2.0, 3.0, 4.0);
        RiskDataDTO dto2 = new RiskDataDTO(1.0, 2.0, 3.0, 4.0);
        RiskDataDTO dto3 = new RiskDataDTO(9.0, 8.0, 7.0, 6.0);
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
    }
}

