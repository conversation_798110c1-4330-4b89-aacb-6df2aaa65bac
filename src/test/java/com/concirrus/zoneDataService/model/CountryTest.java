package com.concirrus.zoneDataService.model;

import org.junit.jupiter.api.Test;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.*;

class CountryTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        Country country = new Country();
        country.setCountryName("India");
        country.setCountryCode("IN");
        assertEquals("India", country.getCountryName());
        assertEquals("IN", country.getCountryCode());
    }

    @Test
    void testAllArgsConstructor() {
        Country country = new Country("UK", "GB");
        assertEquals("UK", country.getCountryName());
        assertEquals("GB", country.getCountryCode());
    }

    @Test
    void testEqualsAndHashCode() {
        Country c1 = new Country("A", "B");
        Country c2 = new Country("A", "B");
        Country c3 = new Country("X", "Y");
        assertEquals(c1, c2);
        assertEquals(c1.hashCode(), c2.hashCode());
        assertNotEquals(c1, c3);
        assertNotEquals(c1, null);
        assertNotEquals(c1, "notACountry");
    }

    @Test
    void testToStringAndNulls() {
        Country c = new Country();
        assertNotNull(c.toString());
        c.setCountryName(null);
        c.setCountryCode(null);
        assertNotNull(c.toString());
    }
}
