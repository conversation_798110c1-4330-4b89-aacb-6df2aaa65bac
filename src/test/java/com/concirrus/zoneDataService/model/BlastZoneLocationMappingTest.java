package com.concirrus.zoneDataService.model;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class BlastZoneLocationMappingTest {
    @Test
    void testBuilder() {
        BlastZoneLocationMapping mapping = BlastZoneLocationMapping.builder()
                .id(1L)
                .blastZoneId(123L)
                .locationId("loc1")
                .submissionId("sub1")
                .distance(10.0)
                .geocodingGrade("A")
                .blastZonePml(100.0)
                .blastZoneTiv(1000.0)
                .pmlZone("PML1")
                .pmlPercentage(50.0)
                .build();
                
        assertEquals(1L, mapping.getId());
        assertEquals(123L, mapping.getBlastZoneId());
        assertEquals("loc1", mapping.getLocationId());
        assertEquals("sub1", mapping.getSubmissionId());
        assertEquals(10.0, mapping.getDistance());
        assertEquals("A", mapping.getGeocodingGrade());
        assertEquals(100.0, mapping.getBlastZonePml());
        assertEquals(1000.0, mapping.getBlastZoneTiv());
        assertEquals("PML1", mapping.getPmlZone());
        assertEquals(50.0, mapping.getPmlPercentage());
    }

    @Test
    void testEqualsAndHashCode() {
        BlastZoneLocationMapping m1 = BlastZoneLocationMapping.builder()
                .id(1L)
                .blastZoneId(1L)
                .locationId("loc1")
                .submissionId("sub1")
                .distance(10.0)
                .geocodingGrade("A")
                .blastZonePml(100.0)
                .blastZoneTiv(1000.0)
                .pmlZone("PML1")
                .pmlPercentage(50.0)
                .build();
                
        BlastZoneLocationMapping m2 = BlastZoneLocationMapping.builder()
                .id(1L)
                .blastZoneId(1L)
                .locationId("loc1")
                .submissionId("sub1")
                .distance(10.0)
                .geocodingGrade("A")
                .blastZonePml(100.0)
                .blastZoneTiv(1000.0)
                .pmlZone("PML1")
                .pmlPercentage(50.0)
                .build();
                
        BlastZoneLocationMapping m3 = BlastZoneLocationMapping.builder()
                .id(2L)
                .blastZoneId(2L)
                .locationId("loc2")
                .submissionId("sub2")
                .distance(20.0)
                .geocodingGrade("B")
                .blastZonePml(200.0)
                .blastZoneTiv(2000.0)
                .pmlZone("PML2")
                .pmlPercentage(60.0)
                .build();
                
        assertEquals(m1, m2);
        assertEquals(m1.hashCode(), m2.hashCode());
        assertNotEquals(m1, m3);
        assertNotEquals(m1, null);
        assertNotEquals(m1, "notAMapping");
    }

    @Test
    void testToString() {
        BlastZoneLocationMapping mapping = BlastZoneLocationMapping.builder()
                .id(1L)
                .blastZoneId(123L)
                .locationId("loc1")
                .submissionId("sub1")
                .build();
                
        assertNotNull(mapping.toString());
        assertTrue(mapping.toString().contains("BlastZoneLocationMapping"));
        assertTrue(mapping.toString().contains("id=1"));
        assertTrue(mapping.toString().contains("blastZoneId=123"));
        assertTrue(mapping.toString().contains("locationId=loc1"));
        assertTrue(mapping.toString().contains("submissionId=sub1"));
    }
}
