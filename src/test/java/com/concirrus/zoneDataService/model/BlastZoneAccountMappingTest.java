package com.concirrus.zoneDataService.model;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class BlastZoneAccountMappingTest {
    @Test
    void testBuilder() {
        BlastZoneAccountMapping mapping = BlastZoneAccountMapping.builder()
                .blastZoneId(123L)
                .accountId("acc1")
                .blastZoneTiv(1000.0)
                .blastZoneExposure(500.0)
                .blastZonePml(250.0)
                .build();
                
        assertEquals(123L, mapping.getBlastZoneId());
        assertEquals("acc1", mapping.getAccountId());
        assertEquals(1000.0, mapping.getBlastZoneTiv());
        assertEquals(500.0, mapping.getBlastZoneExposure());
        assertEquals(250.0, mapping.getBlastZonePml());
    }

    @Test
    void testEqualsAndHashCode() {
        BlastZoneAccountMapping m1 = BlastZoneAccountMapping.builder()
                .blastZoneId(1L)
                .accountId("acc1")
                .blastZoneTiv(1000.0)
                .blastZoneExposure(500.0)
                .blastZonePml(250.0)
                .build();
                
        BlastZoneAccountMapping m2 = BlastZoneAccountMapping.builder()
                .blastZoneId(1L)
                .accountId("acc1")
                .blastZoneTiv(1000.0)
                .blastZoneExposure(500.0)
                .blastZonePml(250.0)
                .build();
                
        BlastZoneAccountMapping m3 = BlastZoneAccountMapping.builder()
                .blastZoneId(2L)
                .accountId("acc2")
                .blastZoneTiv(2000.0)
                .blastZoneExposure(1000.0)
                .blastZonePml(500.0)
                .build();
                
        assertEquals(m1, m2);
        assertEquals(m1.hashCode(), m2.hashCode());
        assertNotEquals(m1, m3);
        assertNotEquals(m1, null);
        assertNotEquals(m1, "notAMapping");
    }

    @Test
    void testToString() {
        BlastZoneAccountMapping mapping = BlastZoneAccountMapping.builder()
                .blastZoneId(123L)
                .accountId("acc1")
                .build();
                
        assertNotNull(mapping.toString());
        assertTrue(mapping.toString().contains("BlastZoneAccountMapping"));
        assertTrue(mapping.toString().contains("blastZoneId=123"));
        assertTrue(mapping.toString().contains("accountId=acc1"));
    }
}
