package com.concirrus.zoneDataService.model;

import org.junit.jupiter.api.Test;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AccountTest {
    @Test
    void testNoArgsConstructorAndSetters() {
        Account acc = new Account();
        acc.setId("id1");
        acc.setQuoteId("q1");
        acc.setAccountName("accName");
        acc.setSubmissionId("sub1");
        acc.setPolicyReference("ref1");
        acc.setBinder("binder1");
        acc.setInceptionDate("2024-01-01");
        acc.setExpiryDate("2024-12-31");
        acc.setTiv(100.0);
        acc.setLimit(200.0);
        acc.setExcess(10.0);
        acc.setDeductible(5.0);
        acc.setLine(1.0);
        acc.setExposure(50.0);
        acc.setPml(20.0);
        acc.setPremium(500.0);
        acc.setPolicyCurrency("USD");
        acc.setPerils(Arrays.asList("Fire", "Flood"));
        assertEquals("id1", acc.getId());
        assertEquals("q1", acc.getQuoteId());
        assertEquals("accName", acc.getAccountName());
        assertEquals("sub1", acc.getSubmissionId());
        assertEquals("ref1", acc.getPolicyReference());
        assertEquals("binder1", acc.getBinder());
        assertEquals("2024-01-01", acc.getInceptionDate());
        assertEquals("2024-12-31", acc.getExpiryDate());
        assertEquals(100.0, acc.getTiv());
        assertEquals(200.0, acc.getLimit());
        assertEquals(10.0, acc.getExcess());
        assertEquals(5.0, acc.getDeductible());
        assertEquals(1.0, acc.getLine());
        assertEquals(50.0, acc.getExposure());
        assertEquals(20.0, acc.getPml());
        assertEquals(500.0, acc.getPremium());
        assertEquals("USD", acc.getPolicyCurrency());
        assertEquals(Arrays.asList("Fire", "Flood"), acc.getPerils());
    }

    @Test
    void testAllArgsConstructor() {
        List<String> perils = Collections.singletonList("Earthquake");
        Account acc = new Account("id2", "q2", "acc2", "sub2", "ref2", "binder2", "2025-01-01", "2025-12-31", 150.0, 250.0, 15.0, 7.0, 2.0, 60.0, 25.0, 600.0, "EUR", perils,4000.0, 10.0);
        assertEquals("id2", acc.getId());
        assertEquals("q2", acc.getQuoteId());
        assertEquals("acc2", acc.getAccountName());
        assertEquals("sub2", acc.getSubmissionId());
        assertEquals("ref2", acc.getPolicyReference());
        assertEquals("binder2", acc.getBinder());
        assertEquals("2025-01-01", acc.getInceptionDate());
        assertEquals("2025-12-31", acc.getExpiryDate());
        assertEquals(150.0, acc.getTiv());
        assertEquals(250.0, acc.getLimit());
        assertEquals(15.0, acc.getExcess());
        assertEquals(7.0, acc.getDeductible());
        assertEquals(2.0, acc.getLine());
        assertEquals(60.0, acc.getExposure());
        assertEquals(25.0, acc.getPml());
        assertEquals(600.0, acc.getPremium());
        assertEquals("EUR", acc.getPolicyCurrency());
        assertEquals(perils, acc.getPerils());
        assertEquals(4000.0, acc.getBinderExposure());
        assertEquals(10.0, acc.getAcuParticipationLinePercentage());
    }

    @Test
    void testEqualsAndHashCode() {
        List<String> perils = Arrays.asList("Fire", "Flood");
        Account acc1 = new Account("id", "q", "a", "s", "r", "b", "i", "e", 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, "USD", perils, 4000.0, 10.0);
        Account acc2 = new Account("id", "q", "a", "s", "r", "b", "i", "e", 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, "USD", perils, 4000.0, 10.0);
        Account acc3 = new Account("idx", "qx", "ax", "sx", "rx", "bx", "ix", "ex", 10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, "EUR", Arrays.asList("Wind"), 4000.0, 10.0);
        assertEquals(acc1, acc2);
        assertEquals(acc1.hashCode(), acc2.hashCode());
        assertNotEquals(acc1, acc3);
    }

    @Test
    void testToStringAndNulls() {
        Account acc = new Account();
        assertNotNull(acc.toString());
        acc.setId(null);
        acc.setQuoteId(null);
        acc.setAccountName(null);
        acc.setSubmissionId(null);
        acc.setPolicyReference(null);
        acc.setBinder(null);
        acc.setInceptionDate(null);
        acc.setExpiryDate(null);
        acc.setTiv(null);
        acc.setLimit(null);
        acc.setExcess(null);
        acc.setDeductible(null);
        acc.setLine(null);
        acc.setExposure(null);
        acc.setPml(null);
        acc.setPremium(null);
        acc.setPolicyCurrency(null);
        acc.setPerils(null);
        assertNotNull(acc.toString());
    }
}

