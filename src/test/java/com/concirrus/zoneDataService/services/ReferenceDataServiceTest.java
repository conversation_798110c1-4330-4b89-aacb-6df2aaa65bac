package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.CountriesRepository;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.model.Country;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class ReferenceDataServiceTest {
    @Mock
    private CountriesRepository countriesRepository;
    @InjectMocks
    private ReferenceDataService referenceDataService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testListCountries_Normal() {
        List<Country> countries = List.of(new Country("India", "IN"));
        when(countriesRepository.listCountries(anyString(), anyInt(), anyInt())).thenReturn(countries);
        when(countriesRepository.countCountries(anyString())).thenReturn(1L);
        CustomPageResponse<Country> resp = referenceDataService.listCountries(0, 10, "foo");
        assertEquals(1L, resp.getTotal());
        assertFalse(resp.isHasMore());
        assertEquals(1, resp.getData().size());
        assertEquals("India", resp.getData().get(0).getCountryName());
    }

    @Test
    void testListCountries_Empty() {
        when(countriesRepository.listCountries(anyString(), anyInt(), anyInt())).thenReturn(Collections.emptyList());
        when(countriesRepository.countCountries(anyString())).thenReturn(0L);
        CustomPageResponse<Country> resp = referenceDataService.listCountries(0, 10, "foo");
        assertEquals(0L, resp.getTotal());
        assertFalse(resp.isHasMore());
        assertTrue(resp.getData().isEmpty());
    }

    @Test
    void testListCountries_NullList() {
        when(countriesRepository.listCountries(anyString(), anyInt(), anyInt())).thenReturn(null);
        when(countriesRepository.countCountries(anyString())).thenReturn(0L);
        CustomPageResponse<Country> resp = referenceDataService.listCountries(0, 10, "foo");
        assertEquals(0L, resp.getTotal());
        assertFalse(resp.isHasMore());
        assertNull(resp.getData());
    }

    @Test
    void testListCountries_HasMore() {
        when(countriesRepository.listCountries(anyString(), anyInt(), anyInt())).thenReturn(List.of(new Country("A", "A")));
        when(countriesRepository.countCountries(anyString())).thenReturn(21L);
        CustomPageResponse<Country> resp = referenceDataService.listCountries(1, 10, "foo");
        assertTrue(resp.isHasMore());
    }

    @Test
    void testListCountries_PageHasMore() {
        List<Country> countries = List.of(new Country("A", "A"), new Country("B", "B"));
        when(countriesRepository.listCountries(anyString(), anyInt(), anyInt())).thenReturn(countries);
        when(countriesRepository.countCountries(anyString())).thenReturn(25L);
        CustomPageResponse<Country> resp = referenceDataService.listCountries(1, 10, "foo");
        assertTrue(resp.isHasMore());
    }

    @Test
    void testListPmlZones() {
        List<String> zones = referenceDataService.listPmlZones();
        assertNotNull(zones);
        assertTrue(zones.contains("Zone A"));
        assertTrue(zones.size() >= 1);
    }

    @Test
    void testListPmlZones_NotNullAndNotEmpty() {
        List<String> zones = referenceDataService.listPmlZones();
        assertNotNull(zones);
        assertFalse(zones.isEmpty());
    }
}
