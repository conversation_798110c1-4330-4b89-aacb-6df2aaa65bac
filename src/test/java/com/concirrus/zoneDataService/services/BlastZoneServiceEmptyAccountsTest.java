package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.AccountRepository;
import com.concirrus.zoneDataService.dal.AnalysisResultRepository;
import com.concirrus.zoneDataService.dal.BlastZoneLocationMappingRepository;
import com.concirrus.zoneDataService.dal.BlastZoneRepository;
import com.concirrus.zoneDataService.dto.BlastZoneDTO;
import com.concirrus.zoneDataService.dto.BlastZoneFilterRequest;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class BlastZoneServiceEmptyAccountsTest {
    @Mock
    private BlastZoneRepository blastZoneRepository;
    @Mock
    private AccountRepository accountRepository;
    @Mock
    private BlastZoneLocationMappingRepository locationMappingRepository;
    @Mock
    private AnalysisResultRepository analysisResultRepository;
    @InjectMocks
    private BlastZoneService blastZoneService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testListBlastZones_EmptyAccounts() {
        try {
            // Setup test data
            BlastZoneFilterRequest req = new BlastZoneFilterRequest();
            req.setAccounts(Collections.emptyList());
            req.setCountry(Collections.emptyList());
            req.setRollUpDate(LocalDate.now()); // Set rollUpDate to avoid NPE in getCollectionName
            Pageable pageable = PageRequest.of(0, 10);
            
            System.out.println("Test setup complete");
            
            // Create a response with empty list (matching repository behavior)
            CustomPageResponse<BlastZoneDTO> mockResponse = CustomPageResponse.<BlastZoneDTO>builder()
                    .total(0L)
                    .hasMore(false)
                    .page(1) // Page is 1-based in the repository
                    .size(10)
                    .data(Collections.emptyList()) // Repository returns empty list
                    .build();
                    
            System.out.println("Created mock response: " + mockResponse);
            
            // Mock the repository to return our response when called with null blast zone IDs
            when(blastZoneRepository.filterBlastZones(
                isNull(), 
                any(), 
                anyString(), 
                anyString(), 
                any(), 
                anyString()
            )).thenReturn(mockResponse);
            
            System.out.println("Mocked blastZoneRepository.filterBlastZones");
            
            // Call the service method
            System.out.println("Calling blastZoneService.listBlastZones...");
            CustomPageResponse<BlastZoneDTO> result = blastZoneService.listBlastZones(req, "sort", "ASC", pageable, false);
            
            System.out.println("Service call completed. Result: " + result);
            
            // Verify the response is not null and has the expected structure
            System.out.println("Verifying response...");
            assertNotNull(result, "Response should not be null");
            System.out.println("Response is not null");
            
            assertNotNull(result.getData(), "Response data should not be null");
            System.out.println("Response data is not null");
            
            assertTrue(result.getData().isEmpty(), "Data list should be empty");
            assertEquals(0L, result.getTotal(), "Total count should be 0");
            assertFalse(result.isHasMore(), "Should not have more results");
            
            // Verify the repository was called with the correct parameters
            // When accounts list is empty, getBlastZoneIdsBySubmissionIds should not be called
            verify(locationMappingRepository, never()).getBlastZoneIdsBySubmissionIds(anyList());
            // Instead, filterBlastZones should be called with null blastZoneIds
            verify(blastZoneRepository).filterBlastZones(
                isNull(),
                any(),
                eq("sort"),
                eq("ASC"),
                eq(pageable),
                anyString()
            );
            
            System.out.println("Test completed successfully");
            
        } catch (Exception e) {
            System.err.println("Test failed with exception: " + e);
            e.printStackTrace();
            throw e;
        }
    }
}
