package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob;
import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisResult;
import com.concirrus.zoneDataService.dto.rest.CustomPageResponse;
import com.concirrus.zoneDataService.sal.MiAnalysisSal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MiAnalysisProxyServiceTest {

    @Mock
    private MiAnalysisSal miAnalysisSal;

    private MiAnalysisProxyService miAnalysisProxyService;

    @BeforeEach
    void setUp() {
        miAnalysisProxyService = new MiAnalysisProxyService(miAnalysisSal);
    }

    @Test
    void testGetMiAnalysisJob() {
        // Arrange
        String clientId = "test-client";
        String submissionId = "sub123";
        String quoteId = "quote456";
        Map<String, String> expectedResult = Map.of("jobId", "job123", "status", "CREATED");

        when(miAnalysisSal.getMiAnalysisJob(clientId, submissionId, quoteId))
                .thenReturn(expectedResult);

        // Act
        Map<String, String> result = miAnalysisProxyService.getMiAnalysisJob(clientId, submissionId, quoteId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(miAnalysisSal).getMiAnalysisJob(clientId, submissionId, quoteId);
    }

    @Test
    void testGetMiAnalysisResults() {
        // Arrange
        String clientId = "test-client";
        String jobId = "job123";
        String sortBy = "currentExposure";
        String sortOrder = "DESC";
        int page = 0;
        int pageSize = 20;
        String peril = "war";

        MiAnalysisResult result1 = new MiAnalysisResult();
        result1.setBlastZoneId("zone1");
        result1.setCurrentExposure(1000.0);

        CustomPageResponse<MiAnalysisResult> expectedResult = CustomPageResponse.of(
                List.of(result1), 1L, page, pageSize, false);

        when(miAnalysisSal.getMiAnalysisResults(clientId, jobId, sortBy, sortOrder, page, pageSize, peril))
                .thenReturn(expectedResult);

        // Act
        CustomPageResponse<MiAnalysisResult> result = miAnalysisProxyService.getMiAnalysisResults(
                clientId, jobId, sortBy, sortOrder, page, pageSize, peril);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        assertEquals(1, result.getContent().size());
        verify(miAnalysisSal).getMiAnalysisResults(clientId, jobId, sortBy, sortOrder, page, pageSize, peril);
    }

    @Test
    void testCreateMiAnalysisJob() {
        // Arrange
        String clientId = "test-client";
        MiAnalysisJobRequest request = new MiAnalysisJobRequest();
        request.setSubmissionId("sub123");
        request.setQuoteId("quote456");

        MiAnalysisJob expectedResult = new MiAnalysisJob();
        expectedResult.setJobId("job123");
        expectedResult.setSubmissionId("sub123");

        when(miAnalysisSal.createMiAnalysisJob(clientId, request))
                .thenReturn(expectedResult);

        // Act
        MiAnalysisJob result = miAnalysisProxyService.createMiAnalysisJob(clientId, request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(miAnalysisSal).createMiAnalysisJob(clientId, request);
    }

    @Test
    void testExecuteMiAnalysisJob() {
        // Arrange
        String clientId = "test-client";
        String jobId = "job123";
        String expectedResult = "JOB Started for Job_Id:job123";

        when(miAnalysisSal.executeMiAnalysisJob(clientId, jobId))
                .thenReturn(expectedResult);

        // Act
        String result = miAnalysisProxyService.executeMiAnalysisJob(clientId, jobId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(miAnalysisSal).executeMiAnalysisJob(clientId, jobId);
    }

    @Test
    void testDeleteMiAnalysisJob() {
        // Arrange
        String clientId = "test-client";
        String submissionId = "sub123";
        String quoteId = "quote456";
        String expectedResult = "Deleted MI Analysis job successfully";

        when(miAnalysisSal.deleteMiAnalysisJob(clientId, submissionId, quoteId))
                .thenReturn(expectedResult);

        // Act
        String result = miAnalysisProxyService.deleteMiAnalysisJob(clientId, submissionId, quoteId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(miAnalysisSal).deleteMiAnalysisJob(clientId, submissionId, quoteId);
    }
}
