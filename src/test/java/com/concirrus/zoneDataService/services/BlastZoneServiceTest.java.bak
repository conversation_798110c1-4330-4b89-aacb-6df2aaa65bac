package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.AccountRepository;
import com.concirrus.zoneDataService.dal.AnalysisResultRepository;
import com.concirrus.zoneDataService.dal.BlastZoneLocationMappingRepository;
import com.concirrus.zoneDataService.dal.BlastZoneRepository;
import com.concirrus.zoneDataService.dto.BlastZoneDTO;
import com.concirrus.zoneDataService.dto.BlastZoneFilterRequest;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.model.BlastZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class BlastZoneServiceTest {
    @Mock
    private BlastZoneRepository blastZoneRepository;
    @Mock
    private AccountRepository accountRepository;
    @Mock
    private BlastZoneLocationMappingRepository locationMappingRepository;
    @Mock
    private AnalysisResultRepository analysisResultRepository;
    @InjectMocks
    private BlastZoneService blastZoneService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUpdateAllBlastZoneRiskScores_AllBranches() {
        BlastZone zoneWithCentre = new BlastZone();
        zoneWithCentre.setId(1L);
        zoneWithCentre.setCentre(null); // triggers log.warn and continue
        BlastZone zoneWithCoords = new BlastZone();
        zoneWithCoords.setId(2L);
        zoneWithCoords.setCentre(mock(org.springframework.data.mongodb.core.geo.GeoJsonPoint.class));
        List<BlastZone> zones = Arrays.asList(zoneWithCentre, zoneWithCoords);
        when(blastZoneRepository.findAll()).thenReturn(zones);
        // Mock RestTemplate and static call
        RestTemplate restTemplate = mock(RestTemplate.class);
        Map<String, Object> riskMap = new HashMap<>();
        riskMap.put("War", 1.0);
        riskMap.put("Terrorism", 2.0);
        riskMap.put("CivilUnrest", 3.0);
        riskMap.put("Combined", 4.0);
        Map<String, Object> body = new HashMap<>();
        body.put("Risk", riskMap);
        ResponseEntity<Map> response = new ResponseEntity<>(body, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(), eq(Map.class))).thenReturn(response);
        // Use spy to inject RestTemplate
        BlastZoneService spyService = spy(blastZoneService);
        doReturn(restTemplate).when(spyService).createRestTemplate();
        doReturn(true).when(spyService).is2xxSuccessful(any());
        doNothing().when(blastZoneRepository).save(any());
        String result = spyService.updateAllBlastZoneRiskScores();
        assertTrue(result.contains("Successfully updated risk scores"));
    }

    @Test
    void testUpdateAllBlastZoneRiskScores_Non2xxResponse() {
        BlastZone zone = new BlastZone();
        zone.setId(3L);
        org.springframework.data.mongodb.core.geo.GeoJsonPoint centre = mock(org.springframework.data.mongodb.core.geo.GeoJsonPoint.class);
        when(centre.getX()).thenReturn(1.0);
        when(centre.getY()).thenReturn(2.0);
        zone.setCentre(centre);
        List<BlastZone> zones = Collections.singletonList(zone);
        when(blastZoneRepository.findAll()).thenReturn(zones);
        RestTemplate restTemplate = mock(RestTemplate.class);
        ResponseEntity<Map> response = new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        when(restTemplate.postForEntity(anyString(), any(), eq(Map.class))).thenReturn(response);
        BlastZoneService spyService = spy(blastZoneService);
        doReturn(restTemplate).when(spyService).createRestTemplate();
        doReturn(false).when(spyService).is2xxSuccessful(any());
        String result = spyService.updateAllBlastZoneRiskScores();
        assertTrue(result.contains("Successfully updated risk scores"));
    }

    @Test
    void testUpdateAllBlastZoneRiskScores_Exception() {
        BlastZone zone = new BlastZone();
        zone.setId(4L);
        org.springframework.data.mongodb.core.geo.GeoJsonPoint centre = mock(org.springframework.data.mongodb.core.geo.GeoJsonPoint.class);
        when(centre.getX()).thenReturn(1.0);
        when(centre.getY()).thenReturn(2.0);
        zone.setCentre(centre);
        List<BlastZone> zones = Collections.singletonList(zone);
        when(blastZoneRepository.findAll()).thenReturn(zones);
        RestTemplate restTemplate = mock(RestTemplate.class);
        when(restTemplate.postForEntity(anyString(), any(), eq(Map.class))).thenThrow(new RuntimeException("fail"));
        BlastZoneService spyService = spy(blastZoneService);
        doReturn(restTemplate).when(spyService).createRestTemplate();
        doReturn(true).when(spyService).is2xxSuccessful(any());
        String result = spyService.updateAllBlastZoneRiskScores();
        assertTrue(result.contains("Successfully updated risk scores"));
    }

    @Test
    void testUpdateAllBlastZoneRiskScores_NullRiskMap() {
        BlastZone zone = new BlastZone();
        zone.setId(5L);
        org.springframework.data.mongodb.core.geo.GeoJsonPoint centre = mock(org.springframework.data.mongodb.core.geo.GeoJsonPoint.class);
        when(centre.getX()).thenReturn(1.0);
        when(centre.getY()).thenReturn(2.0);
        zone.setCentre(centre);
        List<BlastZone> zones = Collections.singletonList(zone);
        when(blastZoneRepository.findAll()).thenReturn(zones);
        RestTemplate restTemplate = mock(RestTemplate.class);
        Map<String, Object> body = new HashMap<>();
        body.put("Risk", null);
        ResponseEntity<Map> response = new ResponseEntity<>(body, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(), eq(Map.class))).thenReturn(response);
        BlastZoneService spyService = spy(blastZoneService);
        doReturn(restTemplate).when(spyService).createRestTemplate();
        doReturn(true).when(spyService).is2xxSuccessful(any());
        String result = spyService.updateAllBlastZoneRiskScores();
        assertTrue(result.contains("Successfully updated risk scores"));
    }

    @Test
    void testUpdateAllBlastZoneRiskScores_MissingRiskFields() {
        BlastZone zone = new BlastZone();
        zone.setId(6L);
        org.springframework.data.mongodb.core.geo.GeoJsonPoint centre = mock(org.springframework.data.mongodb.core.geo.GeoJsonPoint.class);
        when(centre.getX()).thenReturn(1.0);
        when(centre.getY()).thenReturn(2.0);
        zone.setCentre(centre);
        List<BlastZone> zones = Collections.singletonList(zone);
        when(blastZoneRepository.findAll()).thenReturn(zones);
        RestTemplate restTemplate = mock(RestTemplate.class);
        Map<String, Object> riskMap = new HashMap<>();
        // Only one field present
        riskMap.put("War", 1.0);
        Map<String, Object> body = new HashMap<>();
        body.put("Risk", riskMap);
        ResponseEntity<Map> response = new ResponseEntity<>(body, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(), eq(Map.class))).thenReturn(response);
        BlastZoneService spyService = spy(blastZoneService);
        doReturn(restTemplate).when(spyService).createRestTemplate();
        doReturn(true).when(spyService).is2xxSuccessful(any());
        doNothing().when(blastZoneRepository).save(any());
        String result = spyService.updateAllBlastZoneRiskScores();
        assertTrue(result.contains("Successfully updated risk scores"));
    }

    @Test
    void testUpdateAllBlastZoneRiskScores_NullBody() {
        BlastZone zone = new BlastZone();
        zone.setId(7L);
        org.springframework.data.mongodb.core.geo.GeoJsonPoint centre = mock(org.springframework.data.mongodb.core.geo.GeoJsonPoint.class);
        when(centre.getX()).thenReturn(1.0);
        when(centre.getY()).thenReturn(2.0);
        zone.setCentre(centre);
        List<BlastZone> zones = Collections.singletonList(zone);
        when(blastZoneRepository.findAll()).thenReturn(zones);
        RestTemplate restTemplate = mock(RestTemplate.class);
        ResponseEntity<Map> response = new ResponseEntity<>(null, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(), eq(Map.class))).thenReturn(response);
        BlastZoneService spyService = spy(blastZoneService);
        doReturn(restTemplate).when(spyService).createRestTemplate();
        doReturn(true).when(spyService).is2xxSuccessful(any());
        String result = spyService.updateAllBlastZoneRiskScores();
        assertTrue(result.contains("Successfully updated risk scores"));
    }

    @Test
    void testUpdateAllBlastZoneRiskScores_RiskMapNotMapType() {
        BlastZone zone = new BlastZone();
        zone.setId(8L);
        org.springframework.data.mongodb.core.geo.GeoJsonPoint centre = mock(org.springframework.data.mongodb.core.geo.GeoJsonPoint.class);
        when(centre.getX()).thenReturn(1.0);
        when(centre.getY()).thenReturn(2.0);
        zone.setCentre(centre);
        List<BlastZone> zones = Collections.singletonList(zone);
        when(blastZoneRepository.findAll()).thenReturn(zones);
        RestTemplate restTemplate = mock(RestTemplate.class);
        Map<String, Object> body = new HashMap<>();
        body.put("Risk", "notAMap");
        ResponseEntity<Map> response = new ResponseEntity<>(body, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(), eq(Map.class))).thenReturn(response);
        BlastZoneService spyService = spy(blastZoneService);
        doReturn(restTemplate).when(spyService).createRestTemplate();
        doReturn(true).when(spyService).is2xxSuccessful(any());
        String result = spyService.updateAllBlastZoneRiskScores();
        assertTrue(result.contains("Successfully updated risk scores"));
    }

    @Test
    void testListBlastZones_WithAccounts() {
        BlastZoneFilterRequest req = new BlastZoneFilterRequest();
        req.setAccounts(List.of("acc1"));
        req.setCountry(List.of("IN"));
        Pageable pageable = PageRequest.of(0, 10);
        when(locationMappingRepository.getBlastZoneIdsBySubmissionIds(anyList())).thenReturn(List.of(1L));
        CustomPageResponse<BlastZoneDTO> response = new CustomPageResponse<>(1L, false, 0, 10, Collections.emptyList());
        when(blastZoneRepository.filterBlastZones(anyList(), anyList(), anyString(), anyString(), any(), anyString())).thenReturn(response);
        
        CustomPageResponse<BlastZoneDTO> result = blastZoneService.listBlastZones(req, "sort", "ASC", pageable);
        
        assertNotNull(result);
        assertEquals(1L, result.getTotal());
        verify(blastZoneRepository).filterBlastZones(anyList(), anyList(), anyString(), anyString(), any(), anyString());
    }

    @Test
    void testListBlastZones_WithoutAccounts() {
        BlastZoneFilterRequest req = new BlastZoneFilterRequest();
        req.setCountry(List.of("IN"));
        Pageable pageable = PageRequest.of(0, 10);
        CustomPageResponse<BlastZoneDTO> response = new CustomPageResponse<>(1L, false, 0, 10, Collections.emptyList());
        when(blastZoneRepository.filterBlastZones(isNull(), anyList(), anyString(), anyString(), any(), anyString())).thenReturn(response);
        
        CustomPageResponse<BlastZoneDTO> result = blastZoneService.listBlastZones(req, "sort", "ASC", pageable);
        
        assertNotNull(result);
        assertEquals(1L, result.getTotal());
        verify(blastZoneRepository).filterBlastZones(isNull(), anyList(), anyString(), anyString(), any(), anyString());
    }

    @Test
    void testListBlastZones_EmptyAccounts() {
        // Setup test data
        BlastZoneFilterRequest req = new BlastZoneFilterRequest();
        req.setAccounts(Collections.emptyList());
        req.setCountry(Collections.emptyList());
        Pageable pageable = PageRequest.of(0, 10);
        
        // Mock the repository to return empty list of blast zone IDs
        when(locationMappingRepository.getBlastZoneIdsBySubmissionIds(anyList()))
            .thenReturn(Collections.emptyList());
            
        // Create a response with empty list (matching repository behavior)
        CustomPageResponse<BlastZoneDTO> mockResponse = CustomPageResponse.<BlastZoneDTO>builder()
                .total(0L)
                .hasMore(false)
                .page(1) // Page is 1-based in the repository
                .size(10)
                .data(Collections.emptyList()) // Repository returns empty list
                .build();
                
        // Mock the repository to return our response when called with empty blast zone IDs
        when(blastZoneRepository.filterBlastZones(
            eq(Collections.emptyList()), 
            any(), 
            anyString(), 
            anyString(), 
            any(), 
            anyString()
        )).thenReturn(mockResponse);
        
        // Call the service method
        CustomPageResponse<BlastZoneDTO> result = null;
        try {
            result = blastZoneService.listBlastZones(req, "sort", "ASC", pageable);
            System.out.println("Service call completed. Result: " + result);
            if (result != null) {
                System.out.println("Result data: " + result.getData());
                System.out.println("Result total: " + result.getTotal());
                System.out.println("Result hasMore: " + result.isHasMore());
            }
        } catch (Exception e) {
            System.out.println("Exception in listBlastZones: " + e);
            e.printStackTrace();
            throw e;
        }
        
        // Verify the response is not null and has the expected structure
        System.out.println("Verifying response...");
        assertNotNull(result, "Response should not be null");
        assertNotNull(result.getData(), "Response data should not be null");
        assertTrue(result.getData().isEmpty(), "Data list should be empty");
        assertEquals(0L, result.getTotal(), "Total count should be 0");
        assertFalse(result.isHasMore(), "Should not have more results");
        
        // Verify the repository was called with the correct parameters
        verify(locationMappingRepository).getBlastZoneIdsBySubmissionIds(eq(Collections.emptyList()));
        verify(blastZoneRepository).filterBlastZones(
            eq(Collections.emptyList()),
            any(),
            eq("sort"),
            eq("ASC"),
            eq(pageable),
            anyString()
        );
    }

    @Test
    void testListBlastZones_NullAccounts() {
        // Setup test data
        BlastZoneFilterRequest req = new BlastZoneFilterRequest();
        req.setAccounts(null);
        req.setCountry(null);
        Pageable pageable = PageRequest.of(0, 10);
        
        // Create a response with null data (simulating repository behavior)
        CustomPageResponse<BlastZoneDTO> mockResponse = CustomPageResponse.<BlastZoneDTO>builder()
                .total(0L)
                .hasMore(false)
                .page(0)
                .size(10)
                .data(null) // Simulate repository returning null data
                .build();
                
        // Mock the repository to return our response when called with null blast zone IDs
        when(blastZoneRepository.filterBlastZones(
            isNull(), 
            isNull(), 
            anyString(), 
            anyString(), 
            any(), 
            anyString()
        )).thenReturn(mockResponse);
        
        // Call the service method
        CustomPageResponse<BlastZoneDTO> result = blastZoneService.listBlastZones(req, "sort", "ASC", pageable);
        
        // Verify the response is not null and has the expected structure
        assertNotNull(result, "Response should not be null");
        assertNull(result.getData(), "Response data should be null as per repository response");
        assertEquals(0L, result.getTotal(), "Total count should be 0");
        assertFalse(result.isHasMore(), "Should not have more results");
        
        // Verify the repository was called with the correct parameters
        verify(blastZoneRepository).filterBlastZones(
            isNull(),
            isNull(),
            eq("sort"),
            eq("ASC"),
            eq(pageable),
            anyString()
        );
        
        // Verify the mapping repository was not called when accounts is null
        verifyNoInteractions(locationMappingRepository);
    }
}
