package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.AccountRepository;
import com.concirrus.zoneDataService.dal.LocationRepository;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.dto.LocationFilterRequest;
import com.concirrus.zoneDataService.dto.LocationResponse;
import org.apache.coyote.BadRequestException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class LocationServiceTest {
    @Mock
    private LocationRepository locationRepository;
    
    @Mock
    private AccountRepository accountRepository;
    
    @InjectMocks
    private LocationService locationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testListLocations_ValidRequest() throws BadRequestException {
        // Setup test data
        LocationFilterRequest request = new LocationFilterRequest();
        request.setAccountId("test-account");
        
        // Mock repository responses
        when(accountRepository.getSubmissionIdByAccountId("test-account"))
            .thenReturn("test-submission");
            
        LocationResponse locationResponse = new LocationResponse();
        when(locationRepository.fetchLocationResponsesByBlastZoneId(any(), anyString(), anyString(), anyInt(), anyInt()))
            .thenReturn(Collections.singletonList(locationResponse));
            
        when(accountRepository.getPolicyReferenceByAccountId("test-account"))
            .thenReturn("test-policy");
            
        when(locationRepository.countLocationResponsesByBlastZoneId(any()))
            .thenReturn(1L);
        
        // Execute
        CustomPageResponse<LocationResponse> result = locationService.listLocations(request, "name", "ASC", 0, 10);
        
        // Verify
        assertNotNull(result);
        assertEquals(1, result.getData().size());
        assertEquals("test-policy", result.getData().get(0).getPolicyReference());
        
        // Verify repository interactions
        verify(accountRepository).getSubmissionIdByAccountId("test-account");
        verify(locationRepository).fetchLocationResponsesByBlastZoneId(any(), eq("name"), eq("ASC"), eq(0), eq(10));
        verify(accountRepository).getPolicyReferenceByAccountId("test-account");
        verify(locationRepository).countLocationResponsesByBlastZoneId(any());
    }
    
    @Test
    void testListLocations_InvalidAccount() {
        // Setup test data
        LocationFilterRequest request = new LocationFilterRequest();
        request.setAccountId("invalid-account");
        
        // Mock repository to return null submission ID
        when(accountRepository.getSubmissionIdByAccountId("invalid-account"))
            .thenReturn(null);
        
        // Execute and verify exception
        assertThrows(BadRequestException.class, () -> {
            locationService.listLocations(request, "name", "ASC", 0, 10);
        });
        
        // Verify repository interaction
        verify(accountRepository).getSubmissionIdByAccountId("invalid-account");
        verifyNoMoreInteractions(locationRepository);
        verifyNoMoreInteractions(accountRepository);
    }
    
    @Test
    void testListLocations_NullLocationList() throws BadRequestException {
        // Setup test data
        LocationFilterRequest request = new LocationFilterRequest();
        request.setAccountId("test-account");
        
        // Mock repository responses
        when(accountRepository.getSubmissionIdByAccountId("test-account"))
            .thenReturn("test-submission");
            
        // Simulate repository returning empty list for locations
        when(locationRepository.fetchLocationResponsesByBlastZoneId(any(), anyString(), anyString(), anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());
            
        when(accountRepository.getPolicyReferenceByAccountId("test-account"))
            .thenReturn("test-policy");
            
        when(locationRepository.countLocationResponsesByBlastZoneId(any()))
            .thenReturn(0L);
        
        // Execute
        CustomPageResponse<LocationResponse> result = locationService.listLocations(request, "name", "ASC", 0, 10);
        
        // Verify the response is not null and has the expected structure
        assertNotNull(result, "Response should not be null");
        assertNotNull(result.getData(), "Response data should not be null");
        assertTrue(result.getData().isEmpty(), "Response data should be an empty list");
        assertEquals(0L, result.getTotal(), "Total count should be 0");
        assertFalse(result.isHasMore(), "Should not have more results");
        
        // Verify repository interactions
        verify(accountRepository).getSubmissionIdByAccountId("test-account");
        verify(locationRepository).fetchLocationResponsesByBlastZoneId(any(), eq("name"), eq("ASC"), eq(0), eq(10));
        verify(accountRepository).getPolicyReferenceByAccountId("test-account");
        verify(locationRepository).countLocationResponsesByBlastZoneId(any());
    }
}
