package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.AccountRepository;
import com.concirrus.zoneDataService.dal.BlastZoneLocationMappingRepository;
import com.concirrus.zoneDataService.dto.AccountFilterRequest;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.model.Account;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class AccountServiceTest {
    @Mock
    private AccountRepository accountRepository;
    @Mock
    private BlastZoneLocationMappingRepository locationMappingRepository;
    @InjectMocks
    private AccountService accountService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetAllAccountsInBlastZone_AllNulls() {
        Account acc = new Account();
        acc.setTiv(null);
        acc.setLimit(null);
        acc.setExcess(null);
        acc.setDeductible(null);
        List<Account> accounts = Collections.singletonList(acc);
        CustomPageResponse<Account> response = new CustomPageResponse<>(1L, false, 0, 1, accounts);
        when(accountRepository.listAccountsSortedFlexibleV2(any(), anyInt(), anyInt(), anyString(), anyString(), anyBoolean())).thenReturn(response);
        CustomPageResponse<Account> result = accountService.getAllAccountsInBlastZone(new AccountFilterRequest(), 0, 1, "sort", "ASC", false);
        assertEquals(1, result.getData().size());
        assertNull(result.getData().get(0).getExposure());
    }

    @Test
    void testGetAllAccountsInBlastZone_TivGreaterThanSum() {
        Account acc = new Account();
        acc.setTiv(100.0);
        acc.setLimit(50.0);
        acc.setExcess(20.0);
        acc.setDeductible(10.0);
        acc.setLine(100.0);
        acc.setPml(10.0);
        acc.setBinder("binder1");
        List<Account> accounts = Collections.singletonList(acc);
        CustomPageResponse<Account> response = new CustomPageResponse<>(1L, false, 0, 1, accounts);
        when(accountRepository.listAccountsSortedFlexibleV2(any(), anyInt(), anyInt(), anyString(), anyString(), anyBoolean())).thenReturn(response);
        CustomPageResponse<Account> result = accountService.getAllAccountsInBlastZone(new AccountFilterRequest(), 0, 1, "sort", "ASC", false);
        assertEquals(50, result.getData().get(0).getExposure());
    }

    @Test
    void testGetAllAccountsInBlastZone_TivLessThanSum() {
        Account acc = new Account();
        acc.setTiv(5.0);
        acc.setLimit(50.0);
        acc.setExcess(2.0);
        acc.setDeductible(4.0);
        acc.setLine(100.0);
        acc.setPml(10.0);
        acc.setBinder("binder1");
        List<Account> accounts = Collections.singletonList(acc);
        CustomPageResponse<Account> response = new CustomPageResponse<>(1L, false, 0, 1, accounts);
        when(accountRepository.listAccountsSortedFlexibleV2(any(), anyInt(), anyInt(), anyString(), anyString(), anyBoolean())).thenReturn(response);
        CustomPageResponse<Account> result = accountService.getAllAccountsInBlastZone(new AccountFilterRequest(), 0, 1, "sort", "ASC", false);
        assertEquals(0, result.getData().get(0).getExposure());
    }

    @Test
    void testGetAllAccountsInBlastZone_TivInBetween() {
        Account acc = new Account();
        acc.setTiv(20.0);
        acc.setLimit(50.0);
        acc.setExcess(2.0);
        acc.setDeductible(4.0);
        acc.setLine(100.0);
        acc.setPml(10.0);
        acc.setBinder("binder1");
        List<Account> accounts = Collections.singletonList(acc);
        CustomPageResponse<Account> response = new CustomPageResponse<>(1L, false, 0, 1, accounts);
        when(accountRepository.listAccountsSortedFlexibleV2(any(), anyInt(), anyInt(), anyString(), anyString(), anyBoolean())).thenReturn(response);
        CustomPageResponse<Account> result = accountService.getAllAccountsInBlastZone(new AccountFilterRequest(), 0, 1, "sort", "ASC", false);
        assertEquals(14.0, result.getData().get(0).getExposure());
    }

    @Test
    void testGetAllAccountsInBlastZone_EmptyList() {
        CustomPageResponse<Account> response = new CustomPageResponse<>(0L, false, 0, 1, Collections.emptyList());
        when(accountRepository.listAccountsSortedFlexibleV2(any(), anyInt(), anyInt(), anyString(), anyString(), anyBoolean())).thenReturn(response);
        CustomPageResponse<Account> result = accountService.getAllAccountsInBlastZone(new AccountFilterRequest(), 0, 1, "sort", "ASC", false);
        assertTrue(result.getData().isEmpty());
    }
}

