package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.AccountRepository;
import com.concirrus.zoneDataService.dal.AnalysisResultRepository;
import com.concirrus.zoneDataService.dal.BlastZoneRepository;
import com.concirrus.zoneDataService.dal.BlastZoneLocationMappingRepository;
import com.concirrus.zoneDataService.dto.BlastZoneDTO;
import com.concirrus.zoneDataService.dto.BlastZoneFilterRequest;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BlastZoneServiceDebugTest {

    @Mock
    private BlastZoneRepository blastZoneRepository;
    
    @Mock
    private BlastZoneLocationMappingRepository locationMappingRepository;
    
    @Mock
    private AccountRepository accountRepository;
    
    @Mock
    private AnalysisResultRepository analysisResultRepository;

    private BlastZoneService blastZoneService;
    
    @BeforeEach
    void setUp() {
        blastZoneService = new BlastZoneService(
            blastZoneRepository, 
            accountRepository, 
            locationMappingRepository, 
            analysisResultRepository
        );
    }

    @Test
    void debugListBlastZones() {
        try {
            // Setup test data
            BlastZoneFilterRequest req = new BlastZoneFilterRequest();
            req.setAccounts(Collections.emptyList());
            req.setCountry(Collections.emptyList());
            req.setRollUpDate(LocalDate.now());
            Pageable pageable = PageRequest.of(0, 10);

            // Create a mock response
            CustomPageResponse<BlastZoneDTO> mockResponse = CustomPageResponse.<BlastZoneDTO>builder()
                    .total(0L)
                    .hasMore(false)
                    .page(1)
                    .size(10)
                    .data(Collections.emptyList())
                    .build();

            // Mock the repository to return our response with specific argument matchers
            when(blastZoneRepository.filterBlastZones(
                isNull(),  // Expecting null for blastZoneIds when accounts list is empty
                anyList(),
                anyString(),
                anyString(),
                any(),
                anyString()
            )).thenReturn(mockResponse);

            // Call the service method
            System.out.println("Calling blastZoneService.listBlastZones...");
            CustomPageResponse<BlastZoneDTO> result = blastZoneService.listBlastZones(req, "sort", "ASC", pageable, false);
            
            // Print the result
            System.out.println("Result: " + result);
            if (result != null) {
                System.out.println("Result data: " + result.getData());
                System.out.println("Result total: " + result.getTotal());
                System.out.println("Result hasMore: " + result.isHasMore());
            }
            
            // Verify the repository was called with null for blastZoneIds when accounts list is empty
            verify(blastZoneRepository).filterBlastZones(
                isNull(),  // Expecting null for blastZoneIds when accounts list is empty
                any(),
                eq("sort"),
                eq("ASC"),
                eq(pageable),
                anyString()
            );
            
            assertNotNull(result, "Result should not be null");
            
        } catch (Exception e) {
            System.err.println("Test failed with exception: " + e);
            e.printStackTrace();
            throw e;
        }
    }
}
