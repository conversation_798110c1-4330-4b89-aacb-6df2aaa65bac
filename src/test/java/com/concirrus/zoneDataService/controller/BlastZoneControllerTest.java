package com.concirrus.zoneDataService.controller;

import com.concirrus.zoneDataService.dto.*;
import com.concirrus.zoneDataService.exception.GlobalExceptionHandler;
import com.concirrus.zoneDataService.exception.ResourceNotFoundException;
import com.concirrus.zoneDataService.model.AnalysisResult;
import com.concirrus.zoneDataService.services.BlastZoneService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class BlastZoneControllerTest {
    private MockMvc mockMvc;
    @Mock
    private BlastZoneService blastZoneService;
    @InjectMocks
    private BlastZoneController blastZoneController;

    private static final String TEST_CLIENT_ID = "test-client-id-123";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(blastZoneController)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();
    }

    @Test
    void testListBlastZones() throws Exception {
        CustomPageResponse<BlastZoneDTO> response = new CustomPageResponse<>(1L, false, 0, 20, Collections.emptyList());
        when(blastZoneService.listBlastZones(any(), anyString(), anyString(), any(), any())).thenReturn(response);
        String json = "{\"accounts\":[\"a\"],\"country\":[\"c\"],\"rollUpDate\":null}";
        mockMvc.perform(post("/blast-zones/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content(json)
                .param("page", "0")
                .param("size", "20")
                .param("sortBy", "exposure")
                .param("sortOrder", "ASC"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.total").value(1L));
    }

    @Test
    void testSearchBlastZones() throws Exception {
        CustomPageResponse<BlastZoneDTO> response = new CustomPageResponse<>(2L, true, 0, 20, Collections.emptyList());
        when(blastZoneService.searchBlastZone(anyString(), anyInt(), anyInt())).thenReturn(response);
        mockMvc.perform(get("/blast-zones/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "0")
                .param("size", "20")
                .param("searchText", "test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.total").value(2L));
    }

    @Test
    void testGetBlastZoneById() throws Exception {
        BlastZoneDTO dto = new BlastZoneDTO();
        when(blastZoneService.getBlastZoneById(eq("id1"), eq(false))).thenReturn(dto);
        mockMvc.perform(get("/blast-zones/id1")
                .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").exists());
    }

    @Test
    void testGetBlastZoneByIdWithSummary() throws Exception {
        BlastZoneDTO dto = new BlastZoneDTO();
        when(blastZoneService.getBlastZoneById(eq("id2"), eq(true))).thenReturn(dto);
        mockMvc.perform(get("/blast-zones/id2")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("summary", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").exists());
    }

    @Test
    void testGetMarginalImpact() throws Exception {
        List<AnalysisResult> data = new ArrayList<>();
        when(blastZoneService.getMarginalImpactData(anyString(), anyString(), anyInt(), anyInt())).thenReturn(data);
        mockMvc.perform(get("/blast-zones/marginal-impact")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("quoteId", "q1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").isArray());
    }

    @Test
    void testUpdateRiskScores() throws Exception {
        when(blastZoneService.updateAllBlastZoneRiskScores()).thenReturn("updated");
        mockMvc.perform(post("/blast-zones/update-risk-score")
                .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").value("updated"));
    }

    @Test
    void testListBlastZones_NullResult() throws Exception {
        when(blastZoneService.listBlastZones(any(), anyString(), anyString(), any(), any())).thenReturn(null);
        String json = "{\"accounts\":[\"a\"]}";
        mockMvc.perform(post("/blast-zones/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").doesNotExist());
    }

    @Test
    void testSearchBlastZones_NullResult() throws Exception {
        when(blastZoneService.searchBlastZone(anyString(), anyInt(), anyInt())).thenReturn(null);
        mockMvc.perform(get("/blast-zones/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").doesNotExist());
    }

    @Test
    void testGetBlastZoneById_NotFound() throws Exception {
        when(blastZoneService.getBlastZoneById(eq("id3"), eq(false)))
                .thenThrow(new ResourceNotFoundException("Blast Zone", "id3"));
        mockMvc.perform(get("/blast-zones/id3")
                .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(404))
                .andExpect(jsonPath("$.result").doesNotExist());
    }

    @Test
    void testGetMarginalImpact_NullResult() throws Exception {
        when(blastZoneService.getMarginalImpactData(anyString(), anyString(), anyInt(), anyInt())).thenReturn(null);
        mockMvc.perform(get("/blast-zones/marginal-impact")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("quoteId", "q2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").doesNotExist());
    }

    @Test
    void testUpdateRiskScores_NullResult() throws Exception {
        when(blastZoneService.updateAllBlastZoneRiskScores()).thenReturn(null);
        mockMvc.perform(post("/blast-zones/update-risk-score")
                .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").doesNotExist());
    }
}
