package com.concirrus.zoneDataService.controller;

import com.concirrus.zoneDataService.dto.AccountFilterRequest;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.model.Account;
import com.concirrus.zoneDataService.services.AccountService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class AccountControllerTest {
    private MockMvc mockMvc;
    @Mock
    private AccountService accountService;
    @InjectMocks
    private AccountController accountController;

    private static final String TEST_CLIENT_ID = "test-client-id-123";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(accountController).build();
    }

    @Test
    void testSearchAccounts() throws Exception {
        CustomPageResponse<Account> response = CustomPageResponse.<Account>builder()
                .total(1)
                .hasMore(false)
                .page(1)
                .size(2)
                .data(Collections.emptyList())
                .build();
        when(accountService.getAllAccountsInBlastZone(any(AccountFilterRequest.class), anyInt(), anyInt(), anyString(), anyString(), anyBoolean()))
                .thenReturn(response);
        String json = "{\"blastZoneId\":\"z\",\"geoCoding\":\"g\",\"binder\":\"b\",\"pml\":\"p\"}";
        mockMvc.perform(post("/account/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content(json)
                .param("page", "1")
                .param("size", "2")
                .param("sortBy", "premium")
                .param("sortOrder", "ASC"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.total").value(1))
                .andExpect(jsonPath("$.result.hasMore").value(false))
                .andExpect(jsonPath("$.result.page").value(1))
                .andExpect(jsonPath("$.result.size").value(2));
    }

    @Test
    void testSearchAccounts_DefaultParams() throws Exception {
        CustomPageResponse<Account> response = CustomPageResponse.<Account>builder()
                .total(0)
                .hasMore(false)
                .page(0)
                .size(5)
                .data(Collections.emptyList())
                .build();
        when(accountService.getAllAccountsInBlastZone(any(AccountFilterRequest.class), eq(0), eq(5), eq("premium"), eq("ASC"), eq(false)))
                .thenReturn(response);
        String json = "{\"blastZoneId\":\"z\"}";
        mockMvc.perform(post("/account/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.total").value(0))
                .andExpect(jsonPath("$.result.hasMore").value(false))
                .andExpect(jsonPath("$.result.page").value(0))
                .andExpect(jsonPath("$.result.size").value(5));
    }

    @Test
    void testSearchAccounts_NullResult() throws Exception {

        when(accountService.getAllAccountsInBlastZone(any(AccountFilterRequest.class), anyInt(), anyInt(), anyString(), anyString(), anyBoolean()))
                .thenReturn(null);
        String json = "{\"blastZoneId\":\"z\"}";
        mockMvc.perform(post("/account/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").isEmpty());
    }
}
