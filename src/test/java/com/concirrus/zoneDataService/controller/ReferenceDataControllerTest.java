package com.concirrus.zoneDataService.controller;

import com.concirrus.zoneDataService.dto.AccountListDTO;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.model.Country;
import com.concirrus.zoneDataService.services.AccountService;
import com.concirrus.zoneDataService.services.ReferenceDataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class ReferenceDataControllerTest {
    private MockMvc mockMvc;
    @Mock
    private AccountService accountService;
    @Mock
    private ReferenceDataService referenceDataService;
    @InjectMocks
    private ReferenceDataController referenceDataController;

    private static final String TEST_CLIENT_ID = "test-client-id-123";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(referenceDataController).build();
    }

    @Test
    void testGetAccountNames() throws Exception {
        AccountListDTO dto = new AccountListDTO();
        dto.setAccountName("accounts");
        dto.setAccountId("id1");
        CustomPageResponse<AccountListDTO> response = new CustomPageResponse<>(1L, false, 1, 2, List.of(dto));
        when(accountService.listAccountNames(anyInt(), anyInt(), anyString(), anyBoolean(), anyString(), anyString())).thenReturn(response);
        mockMvc.perform(get("/reference/account/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "1")
                .param("size", "2")
                .param("searchText", "foo"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result.data[0].accountName").value("accounts"));
    }

    @Test
    void testGetCountries() throws Exception {
        CustomPageResponse<Country> response = new CustomPageResponse<>(1L, false, 1, 2, List.of(new Country("India", "IN")));
        when(referenceDataService.listCountries(anyInt(), anyInt(), anyString())).thenReturn(response);
        mockMvc.perform(get("/reference/countries/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "1")
                .param("size", "2")
                .param("searchText", "bar"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result.total").value(1L))
                .andExpect(jsonPath("$.result.data[0].countryName").value("India"));
    }

    @Test
    void testGetPmlZones() throws Exception {
        when(referenceDataService.listPmlZones()).thenReturn(List.of("Zone A", "Zone B"));
        mockMvc.perform(get("/reference/pml-zone")
                .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result[0]").value("Zone A"));
    }


    @Test
    void testGetAccountNames_DefaultParams() throws Exception {
        AccountListDTO dto = new AccountListDTO();
        dto.setAccountName("defaultAccounts");
        dto.setAccountId("id2");

        CustomPageResponse<AccountListDTO> response = new CustomPageResponse<>(1L, false, 0, 10, List.of(dto));

        when(accountService.listAccountNames(eq(0), eq(10), isNull(), eq(false), eq("accountName"), eq("DESC")))
                .thenReturn(response);

        mockMvc.perform(get("/reference/account/search")
                        .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isOk());
    }

    @Test
    void testGetCountries_DefaultParams() throws Exception {
        CustomPageResponse<Country> response = new CustomPageResponse<>(1L, false, 0, 10, List.of(new Country("Default", "DF")));
        when(referenceDataService.listCountries(eq(0), eq(10), isNull())).thenReturn(response);
        mockMvc.perform(get("/reference/countries/search")
                .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.data[0].countryName").value("Default"));
    }

    @Test
    void testGetPmlZones_NullResult() throws Exception {
        when(referenceDataService.listPmlZones()).thenReturn(null);
        mockMvc.perform(get("/reference/pml-zone")
                .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").doesNotExist());
    }

    @Test
    void testGetAccountNames_NullResult() throws Exception {
        when(accountService.listAccountNames(anyInt(), anyInt(), anyString(), anyBoolean(), anyString(), anyString())).thenReturn(null);
        mockMvc.perform(get("/reference/account/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "1")
                .param("size", "2")
                .param("searchText", "foo"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").doesNotExist());
    }

    @Test
    void testGetCountries_NullResult() throws Exception {
        when(referenceDataService.listCountries(anyInt(), anyInt(), anyString())).thenReturn(null);
        mockMvc.perform(get("/reference/countries/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "1")
                .param("size", "2")
                .param("searchText", "bar"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").doesNotExist());
    }

    @Test
    void testGetAccountNames_EmptyList() throws Exception {
        CustomPageResponse<AccountListDTO> response = new CustomPageResponse<>(0L, false, 1, 2, Collections.emptyList());
        when(accountService.listAccountNames(anyInt(), anyInt(), anyString(), anyBoolean(), anyString(), anyString())).thenReturn(response);
        mockMvc.perform(get("/reference/account/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "1")
                .param("size", "2")
                .param("searchText", "foo"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result.data").isArray())
                .andExpect(jsonPath("$.result.data").isEmpty());
    }

    @Test
    void testGetCountries_EmptyData() throws Exception {
        CustomPageResponse<Country> response = new CustomPageResponse<>(0L, false, 1, 2, Collections.emptyList());
        when(referenceDataService.listCountries(anyInt(), anyInt(), anyString())).thenReturn(response);
        mockMvc.perform(get("/reference/countries/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "1")
                .param("size", "2")
                .param("searchText", "bar"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.total").value(0L))
                .andExpect(jsonPath("$.result.data").isArray())
                .andExpect(jsonPath("$.result.data").isEmpty());
    }

    @Test
    void testGetPmlZones_EmptyList() throws Exception {
        when(referenceDataService.listPmlZones()).thenReturn(Collections.emptyList());
        mockMvc.perform(get("/reference/pml-zone")
                .header(CLIENT_ID, TEST_CLIENT_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").isArray())
                .andExpect(jsonPath("$.result").isEmpty());
    }

    @Test
    void testGetCountries_NullDataList() throws Exception {
        // CustomPageResponse with null data list
        CustomPageResponse<Country> response = new CustomPageResponse<>(0L, false, 1, 2, null);
        when(referenceDataService.listCountries(anyInt(), anyInt(), anyString())).thenReturn(response);
        mockMvc.perform(get("/reference/countries/search")
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .param("page", "1")
                .param("size", "2")
                .param("searchText", "bar"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.total").value(0L))
                .andExpect(jsonPath("$.result.data").doesNotExist());
    }
}
