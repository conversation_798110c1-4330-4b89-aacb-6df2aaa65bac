package com.concirrus.zoneDataService.controller;

import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.dto.LocationFilterRequest;
import com.concirrus.zoneDataService.dto.LocationResponse;
import com.concirrus.zoneDataService.services.LocationService;
import org.apache.coyote.BadRequestException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class LocationControllerTest {
    private MockMvc mockMvc;

    @Mock
    private LocationService locationService;

    @InjectMocks
    private LocationController locationController;

    private static final String TEST_CLIENT_ID = "test-client-id-123";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(locationController).build();
    }

    @Test
    void testListLocations_Success() throws Exception {
        // Given
        CustomPageResponse<LocationResponse> expectedResponse = new CustomPageResponse<>(
            10L, false, 1, 10, Collections.emptyList());
            
        when(locationService.listLocations(any(LocationFilterRequest.class), any(), any(), anyInt(), anyInt()))
                .thenReturn(expectedResponse);
        
        String requestBody = "{\"accountId\":\"test123\"}";
        
        // When & Then
        mockMvc.perform(post("/locations/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content(requestBody)
                .param("page", "1")
                .param("size", "10")
                .param("sortBy", "name")
                .param("sortOrder", "asc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").isMap())
                .andExpect(jsonPath("$.result.total").value(10))
                .andExpect(jsonPath("$.result.hasMore").value(false))
                .andExpect(jsonPath("$.result.page").value(1))
                .andExpect(jsonPath("$.result.size").value(10));
    }

    @Test
    void testListLocations_WithDefaultPagination() throws Exception {
        // Given
        CustomPageResponse<LocationResponse> expectedResponse = new CustomPageResponse<>(
            5L, false, 0, 5, Collections.emptyList());
            
        when(locationService.listLocations(any(LocationFilterRequest.class), isNull(), isNull(), eq(0), eq(5)))
                .thenReturn(expectedResponse);
        
        // When & Then
        mockMvc.perform(post("/locations/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content("{}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").isMap())
                .andExpect(jsonPath("$.result.total").value(5))
                .andExpect(jsonPath("$.result.hasMore").value(false))
                .andExpect(jsonPath("$.result.page").value(0))
                .andExpect(jsonPath("$.result.size").value(5));
    }

    @Test
    void testListLocations_BadRequest() throws Exception {
        // Given
        String errorMessage = "Invalid request parameters";
        when(locationService.listLocations(any(LocationFilterRequest.class), any(), any(), anyInt(), anyInt()))
                .thenThrow(new BadRequestException(errorMessage));
        
        // When & Then
        mockMvc.perform(post("/locations/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content("{}"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.error").value(errorMessage));
    }

    @Test
    void testListLocations_InternalServerError() throws Exception {
        // Given
        String errorMessage = "Internal server error";
        when(locationService.listLocations(any(LocationFilterRequest.class), any(), any(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException(errorMessage));
        
        // When & Then
        mockMvc.perform(post("/locations/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content("{}"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.status").value(500))
                .andExpect(jsonPath("$.error").value("Internal server error"));
    }

    @Test
    void testListLocations_WithInvalidPagination() throws Exception {
        // Given
        CustomPageResponse<LocationResponse> expectedResponse = new CustomPageResponse<>(
            0L, false, -1, 0, Collections.emptyList());
            
        when(locationService.listLocations(any(LocationFilterRequest.class), isNull(), isNull(), eq(-1), eq(0)))
                .thenReturn(expectedResponse);
        
        // When & Then
        mockMvc.perform(post("/locations/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content("{}")
                .param("page", "-1")
                .param("size", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").isMap())
                .andExpect(jsonPath("$.result.total").value(0))
                .andExpect(jsonPath("$.result.hasMore").value(false))
                .andExpect(jsonPath("$.result.page").value(-1))
                .andExpect(jsonPath("$.result.size").value(0));
    }

    @Test
    void testListLocations_InvalidSortOrder() throws Exception {
        // When & Then
        mockMvc.perform(post("/locations/list")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CLIENT_ID, TEST_CLIENT_ID)
                .content("{}")
                .param("sortOrder", "INVALID"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.error").value("Invalid sortOrder. Must be ASC or DESC"));
    }
}
