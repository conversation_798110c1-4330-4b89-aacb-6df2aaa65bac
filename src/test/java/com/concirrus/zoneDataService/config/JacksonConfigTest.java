package com.concirrus.zoneDataService.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class JacksonConfigTest {
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        JacksonConfig config = new JacksonConfig();
        SimpleModule module = config.doubleSerializerModule();
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(module);
    }

    static class DoubleWrapper {
        public Double value;
        public DoubleWrapper(Double value) { this.value = value; }
    }

    @Test
    public void testDoubleSerialization_roundsToTwoDecimalPlaces() throws JsonProcessingException {
        DoubleWrapper wrapper = new DoubleWrapper(123.4567);
        String json = objectMapper.writeValueAsString(wrapper);
        assertTrue(json.contains("123.46"));
    }

    @Test
    public void testDoubleSerialization_handlesNull() throws JsonProcessingException {
        DoubleWrapper wrapper = new DoubleWrapper(null);
        String json = objectMapper.writeValueAsString(wrapper);
        assertTrue(json.contains("null"));
    }

    @Test
    public void testDoubleSerialization_exactTwoDecimals() throws JsonProcessingException {
        DoubleWrapper wrapper = new DoubleWrapper(10.10);
        String json = objectMapper.writeValueAsString(wrapper);
        assertTrue(json.contains("10.10"));
    }
}

