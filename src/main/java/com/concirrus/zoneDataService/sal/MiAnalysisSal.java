package com.concirrus.zoneDataService.sal;

import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob;
import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisResult;
import com.concirrus.zoneDataService.dto.rest.CustomPageResponse;
import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

@Component
@Slf4j
public class MiAnalysisSal {
    
    private final RestTemplate restTemplate;
    private final String submissionNamespace;
    private static final String BASE_URL = "http://blast-zone-processor-service.";
    private static final String MI_ANALYSIS_BASE_PATH = "/mi-analysis";
    private static final String JOBS_PATH = "/jobs";
    
    public MiAnalysisSal(RestTemplate restTemplate, 
                        @Value("${namespace.submission}") String submissionNamespace) {
        this.restTemplate = restTemplate;
        this.submissionNamespace = submissionNamespace;
    }
    
    /**
     * Get MI analysis job by submission ID and quote ID
     */
    public Map<String, String> getMiAnalysisJob(String clientId, String submissionId, String quoteId) {
        validateClientId(clientId);
        validateParameter(submissionId, "submissionId");
        validateParameter(quoteId, "quoteId");
        
        String url = UriComponentsBuilder
                .fromHttpUrl(BASE_URL + submissionNamespace + MI_ANALYSIS_BASE_PATH + JOBS_PATH)
                .queryParam("submissionId", submissionId)
                .queryParam("quoteId", quoteId)
                .toUriString();
        
        try {
            log.info("Getting MI analysis job for clientId: {}, submissionId: {}, quoteId: {}", 
                    clientId, submissionId, quoteId);
            
            HttpHeaders headers = createHeaders(clientId);
            HttpEntity<?> entity = new HttpEntity<>(headers);
            
            ResponseEntity<BasicResponse<Map<String, String>>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<BasicResponse<Map<String, String>>>() {}
            );
            
            return extractResultFromResponse(response, "MI analysis job");
            
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("HTTP error while fetching MI analysis job for client {}: {} - {}", 
                    clientId, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new MiAnalysisException("Failed to fetch MI analysis job due to HTTP error: " + ex.getStatusCode(), ex);
        } catch (RestClientException ex) {
            log.error("Network error while calling blast zone processor service for MI analysis job {}: {}", 
                    clientId, ex.getMessage());
            throw new MiAnalysisException("Network error while calling blast zone processor service", ex);
        }
    }
    
    /**
     * Get MI analysis results with pagination
     */
    public CustomPageResponse<MiAnalysisResult> getMiAnalysisResults(String clientId, String jobId, 
                                                                    String sortBy, String sortOrder, 
                                                                    int page, int pageSize, String peril) {
        validateClientId(clientId);
        validateParameter(jobId, "jobId");
        
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromHttpUrl(BASE_URL + submissionNamespace + MI_ANALYSIS_BASE_PATH)
                .queryParam("jobId", jobId)
                .queryParam("sortBy", sortBy != null ? sortBy : "currentExposure")
                .queryParam("sortOrder", sortOrder != null ? sortOrder : "DESC")
                .queryParam("page", page)
                .queryParam("pageSize", pageSize);
        
        if (peril != null && !peril.trim().isEmpty()) {
            builder.queryParam("peril", peril);
        }
        
        String url = builder.toUriString();
        
        try {
            log.info("Getting MI analysis results for clientId: {}, jobId: {}, peril: {}, page: {}, pageSize: {}", 
                    clientId, jobId, peril, page, pageSize);
            
            HttpHeaders headers = createHeaders(clientId);
            HttpEntity<?> entity = new HttpEntity<>(headers);
            
            ResponseEntity<BasicResponse<CustomPageResponse<MiAnalysisResult>>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<BasicResponse<CustomPageResponse<MiAnalysisResult>>>() {}
            );
            
            return extractResultFromResponse(response, "MI analysis results");
            
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("HTTP error while fetching MI analysis results for client {}: {} - {}", 
                    clientId, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new MiAnalysisException("Failed to fetch MI analysis results due to HTTP error: " + ex.getStatusCode(), ex);
        } catch (RestClientException ex) {
            log.error("Network error while calling blast zone processor service for MI analysis results {}: {}", 
                    clientId, ex.getMessage());
            throw new MiAnalysisException("Network error while calling blast zone processor service", ex);
        }
    }
    
    /**
     * Create MI analysis job
     */
    public MiAnalysisJob createMiAnalysisJob(String clientId, MiAnalysisJobRequest request) {
        validateClientId(clientId);
        validateParameter(request, "request");
        
        String url = BASE_URL + submissionNamespace + MI_ANALYSIS_BASE_PATH + JOBS_PATH;
        
        try {
            log.info("Creating MI analysis job for clientId: {}, request: {}", clientId, request);
            
            HttpHeaders headers = createHeaders(clientId);
            HttpEntity<MiAnalysisJobRequest> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<BasicResponse<MiAnalysisJob>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<BasicResponse<MiAnalysisJob>>() {}
            );
            
            return extractResultFromResponse(response, "MI analysis job creation");
            
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("HTTP error while creating MI analysis job for client {}: {} - {}", 
                    clientId, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new MiAnalysisException("Failed to create MI analysis job due to HTTP error: " + ex.getStatusCode(), ex);
        } catch (RestClientException ex) {
            log.error("Network error while calling blast zone processor service for MI analysis job creation {}: {}", 
                    clientId, ex.getMessage());
            throw new MiAnalysisException("Network error while calling blast zone processor service", ex);
        }
    }
    
    /**
     * Execute MI analysis job
     */
    public String executeMiAnalysisJob(String clientId, String jobId) {
        validateClientId(clientId);
        validateParameter(jobId, "jobId");
        
        String url = BASE_URL + submissionNamespace + MI_ANALYSIS_BASE_PATH + "/job/" + jobId;
        
        try {
            log.info("Executing MI analysis job for clientId: {}, jobId: {}", clientId, jobId);
            
            HttpHeaders headers = createHeaders(clientId);
            HttpEntity<?> entity = new HttpEntity<>(headers);
            
            ResponseEntity<BasicResponse<String>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<BasicResponse<String>>() {}
            );
            
            return extractResultFromResponse(response, "MI analysis job execution");
            
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("HTTP error while executing MI analysis job for client {}: {} - {}", 
                    clientId, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new MiAnalysisException("Failed to execute MI analysis job due to HTTP error: " + ex.getStatusCode(), ex);
        } catch (RestClientException ex) {
            log.error("Network error while calling blast zone processor service for MI analysis job execution {}: {}", 
                    clientId, ex.getMessage());
            throw new MiAnalysisException("Network error while calling blast zone processor service", ex);
        }
    }
    
    /**
     * Delete MI analysis job
     */
    public String deleteMiAnalysisJob(String clientId, String submissionId, String quoteId) {
        validateClientId(clientId);
        validateParameter(submissionId, "submissionId");
        validateParameter(quoteId, "quoteId");
        
        String url = UriComponentsBuilder
                .fromHttpUrl(BASE_URL + submissionNamespace + MI_ANALYSIS_BASE_PATH)
                .queryParam("submissionId", submissionId)
                .queryParam("quoteId", quoteId)
                .toUriString();
        
        try {
            log.info("Deleting MI analysis job for clientId: {}, submissionId: {}, quoteId: {}", 
                    clientId, submissionId, quoteId);
            
            HttpHeaders headers = createHeaders(clientId);
            HttpEntity<?> entity = new HttpEntity<>(headers);
            
            ResponseEntity<BasicResponse<String>> response = restTemplate.exchange(
                    url,
                    HttpMethod.DELETE,
                    entity,
                    new ParameterizedTypeReference<BasicResponse<String>>() {}
            );
            
            return extractResultFromResponse(response, "MI analysis job deletion");
            
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("HTTP error while deleting MI analysis job for client {}: {} - {}", 
                    clientId, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new MiAnalysisException("Failed to delete MI analysis job due to HTTP error: " + ex.getStatusCode(), ex);
        } catch (RestClientException ex) {
            log.error("Network error while calling blast zone processor service for MI analysis job deletion {}: {}", 
                    clientId, ex.getMessage());
            throw new MiAnalysisException("Network error while calling blast zone processor service", ex);
        }
    }
    
    // Helper methods
    private HttpHeaders createHeaders(String clientId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("client-id", clientId);
        return headers;
    }
    
    private void validateClientId(String clientId) {
        if (clientId == null || clientId.trim().isEmpty()) {
            throw new IllegalArgumentException("Client ID cannot be null or empty");
        }
    }
    
    private void validateParameter(Object parameter, String parameterName) {
        if (parameter == null) {
            throw new IllegalArgumentException(parameterName + " cannot be null");
        }
        if (parameter instanceof String && ((String) parameter).trim().isEmpty()) {
            throw new IllegalArgumentException(parameterName + " cannot be empty");
        }
    }
    
    private <T> T extractResultFromResponse(ResponseEntity<BasicResponse<T>> response, String operation) {
        if (response.getStatusCode() != HttpStatus.OK && response.getStatusCode() != HttpStatus.CREATED) {
            log.warn("Unexpected status code {} for {}", response.getStatusCode(), operation);
            throw new MiAnalysisException("Unexpected response status: " + response.getStatusCode());
        }
        
        BasicResponse<T> responseBody = response.getBody();
        if (responseBody == null) {
            log.error("Response body is null for {}", operation);
            throw new MiAnalysisException("Received null response body from blast zone processor service");
        }
        
        T result = responseBody.getResult();
        if (result == null) {
            log.error("Result is null for {}", operation);
            throw new MiAnalysisException("Result is null in response");
        }
        
        log.info("Successfully completed {}", operation);
        return result;
    }
    
    // Custom exception for better error handling
    public static class MiAnalysisException extends RuntimeException {
        public MiAnalysisException(String message) {
            super(message);
        }
        
        public MiAnalysisException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
