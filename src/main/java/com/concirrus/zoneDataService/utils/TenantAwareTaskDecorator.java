package com.concirrus.zoneDataService.utils;

import com.concirrus.zoneDataService.config.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.task.TaskDecorator;

@Slf4j
public class TenantAwareTaskDecorator implements TaskDecorator {

    @NotNull
    @Override
    public Runnable decorate(@NotNull Runnable runnable) {
        // Capture the tenant context from the current thread
        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        // Return a wrapped runnable that sets context in async thread
        return () -> {
            try {
                // Set tenant context in async thread
                if (tenantId != null) {
                    TenantContextHolder.setTenantContext(tenantId, tenantAlias);
                }

                // Execute the original task
                runnable.run();

            } finally {
                // Always clean up
                log.info("Clearing tenant context after task execution");
                TenantContextHolder.clear();
            }
        };
    }
}