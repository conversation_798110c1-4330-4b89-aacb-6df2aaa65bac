package com.concirrus.zoneDataService.exception;

import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import org.apache.coyote.BadRequestException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;

@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<BasicResponse<?>> handleResourceNotFoundException(ResourceNotFoundException e) {
        BasicResponse<?> response = new BasicResponse<>()
                .setStatus(404)
                .setError(e.getMessage())
                .setResult(null);
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    @ExceptionHandler(BadRequestException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<BasicResponse<?>> handleBadRequestException(BadRequestException e) {
        BasicResponse<?> response = new BasicResponse<>()
                .setStatus(400)
                .setError(e.getMessage())
                .setResult(null);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<BasicResponse<?>> handleGenericException(Exception e) {
        BasicResponse<?> response = new BasicResponse<>()
                .setStatus(500)
                .setError("An unexpected error occurred")
                .setResult(null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}