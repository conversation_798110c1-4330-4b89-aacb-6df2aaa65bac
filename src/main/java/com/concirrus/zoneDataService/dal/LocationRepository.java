package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.dto.LocationFilterRequest;
import com.concirrus.zoneDataService.dto.LocationResponse;
import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;


@Repository
@RequiredArgsConstructor
public class LocationRepository {

    private final MongoTemplate mongoTemplate;


    public List<LocationResponse> fetchLocationResponsesByBlastZoneId(LocationFilterRequest filter,String sortBy, String sortOrder, int page, int size) {
        List<AggregationOperation> operations = new ArrayList<>();

        if (filter.getBlastZoneId() == null) {
            throw new IllegalArgumentException("blastZoneId is required");
        }

        // Build dynamic match criteria
        Criteria criteria = Criteria.where("blastZoneId").is(Long.parseLong(filter.getBlastZoneId()));

        if (StringUtils.hasText(filter.getSubmissionId())) {
            criteria.and("submissionId").is(filter.getSubmissionId());
        }

        if (StringUtils.hasText(filter.getGeocoding())) {
            criteria.and("geocodingGrade").is(filter.getGeocoding());
        }

        if (StringUtils.hasText(filter.getPmlZone())) {
            criteria.and("pmlZone").is(filter.getPmlZone());
        }

        operations.add(match(criteria));

        // Lookup and unwind location
        operations.add(lookup("locations", "locationId", "_id", "location"));
        operations.add(unwind("location"));

        // Projection
        operations.add(project()
                .and("location.locationName").as("locationName")
                .and("location.street").as("street")
                .and("location.city").as("city")
                .and("location.postCode").as("postCode")
                .and("location.longitude").as("longitude")
                .and("location.latitude").as("latitude")
                .and("geocodingGrade").as("geocoding")
                .and("location.geometry").as("centre")
                .and("location.tiv").as("tiv")
                .and("pmlZone").as("pmlZone")
                .and("pmlPercentage").as("pmlPercentage")
                .and("blastZonePml").as("pmlTiv"));

        // Sorting

        if (sortBy != null) {
            Sort.Direction direction = "desc".equalsIgnoreCase(sortOrder) ? Sort.Direction.DESC : Sort.Direction.ASC;
            operations.add(sort(Sort.by(direction, sortBy)));
        }
        // Pagination
        operations.add(skip((long) page * size));
        operations.add(limit(size));

        Aggregation aggregation = newAggregation(operations);

        return mongoTemplate.aggregate(aggregation, "blast_zone_location_mapping", LocationResponse.class)
                .getMappedResults();
    }

    public long countLocationResponsesByBlastZoneId(LocationFilterRequest filter) {
        List<AggregationOperation> operations = new ArrayList<>();

        if (filter.getBlastZoneId() == null) {
            throw new IllegalArgumentException("blastZoneId is required");
        }

        Criteria criteria = Criteria.where("blastZoneId").is(Long.parseLong(filter.getBlastZoneId()));

        if (StringUtils.hasText(filter.getSubmissionId())) {
            criteria.and("submissionId").is(filter.getSubmissionId());
        }

        if (StringUtils.hasText(filter.getGeocoding())) {
            criteria.and("geocodingGrade").is(filter.getGeocoding());
        }

        if (StringUtils.hasText(filter.getPmlZone())) {
            criteria.and("pmlZone").is(filter.getPmlZone());
        }

        operations.add(match(criteria));
        operations.add(count().as("total"));

        Aggregation aggregation = newAggregation(operations);

        AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, "blast_zone_location_mapping", Document.class);

        Document countDoc = results.getUniqueMappedResult();
        return countDoc != null ? countDoc.getInteger("total") : 0L;
    }
}
