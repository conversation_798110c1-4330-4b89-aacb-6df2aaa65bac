package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.dto.BlastZoneDTO;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.dto.MyMapper;
import com.concirrus.zoneDataService.model.BlastZone;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@RequiredArgsConstructor
public class BlastZoneRepository {

    private final MongoTemplate mongoTemplate;
    private final MyMapper mapper;



    public CustomPageResponse<BlastZoneDTO> filterBlastZones(List<Long>blastZoneIds, List<String>country, String sortBy, String sortOrder, Pageable pageable, String collectionName) {
        Query query = new Query();
        query.fields().exclude("geometry");
        
        // for BOUND blast zones
        query.addCriteria(Criteria.where("active").is(true));

        // Filter by blastZoneIds if provided
        if (blastZoneIds != null) {
            query.addCriteria(Criteria.where("_id").in(blastZoneIds));
        }

        // Filter by country if provided
        if (country != null && !country.isEmpty()) {
            query.addCriteria(Criteria.where("country").in(country));
        }

        // Sorting
        if (sortBy != null && !sortBy.isBlank()) {
            Sort.Direction direction = "desc".equalsIgnoreCase(sortOrder) ? Sort.Direction.DESC : Sort.Direction.ASC;
            query.with(Sort.by(direction, sortBy));
        }

        // Total count before pagination
        long total = mongoTemplate.count(query, BlastZone.class,collectionName);

        // Apply pagination
        query.with(pageable);
        List<BlastZone> results = mongoTemplate.find(query, BlastZone.class,collectionName);

        List<BlastZoneDTO>modified = results.stream().map(mapper::fromEntity).toList();

        boolean hasMore = (long) (pageable.getPageNumber() + 1) * pageable.getPageSize() < total;

        return CustomPageResponse.<BlastZoneDTO>builder()
                .total(total)
                .hasMore(hasMore)
                .page(pageable.getPageNumber() + 1)
                .size(pageable.getPageSize())
                .data(modified)
                .build();
    }

    public CustomPageResponse<BlastZoneDTO> searchBlastZones(String searchText, Pageable pageable) {
        Query query = new Query();
        query.addCriteria(Criteria.where("active").is(true));
        if (searchText != null && !searchText.isBlank()) {
            Criteria nameCriteria = Criteria.where("name").regex(searchText, "i");
            Criteria countryCriteria = Criteria.where("country").regex(searchText, "i");
            Criteria cityCriteria = Criteria.where("city").regex(searchText, "i");

            query.addCriteria(new Criteria().orOperator(nameCriteria, countryCriteria, cityCriteria));
        }

        long total = mongoTemplate.count(query, BlastZone.class);

        query.with(pageable);
        List<BlastZone> results = mongoTemplate.find(query, BlastZone.class);

        boolean hasMore = (long) (pageable.getPageNumber() + 1) * pageable.getPageSize() < total;;

        List<BlastZoneDTO>modified = results.stream().map(mapper::fromEntity).toList();

        return CustomPageResponse.<BlastZoneDTO>builder()
                .total(total)
                .hasMore(hasMore)
                .page(pageable.getPageNumber()+1)
                .size(pageable.getPageSize())
                .data(modified)
                .build();
    }

    public BlastZoneDTO findById(String id){
        try {
            long blastZoneId = Long.parseLong(id);
            Query query = new Query(Criteria.where("_id").is(blastZoneId));
            query.fields().exclude("geometry");
            BlastZone blastZone = mongoTemplate.findOne(query, BlastZone.class);
            return mapper.fromEntity(blastZone);
        } catch (NumberFormatException e) {
            // Invalid ID format, return null to indicate not found
            return null;
        }
    }

    public List<BlastZone> findAll() {
        return mongoTemplate.findAll(BlastZone.class);
    }
    public List<BlastZone> findAll(String sortBy, String sortOrder, int page, int size) {
        Sort.Direction direction = sortOrder.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
        Sort sort = Sort.by(direction, sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Query query = new Query().with(pageable);
        return mongoTemplate.find(query, BlastZone.class);
    }


    public void save(BlastZone zone) {
        mongoTemplate.save(zone);
    }
}
