package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.model.AnalysisResult;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class AnalysisResultRepository {

    private final MongoTemplate mongoTemplate;

    public List<AnalysisResult> findAnalysisResults(String sortBy, String sortOrder, int page, int size) {
        // Default sort by currentExposure if null or empty
        String sortField = (sortBy == null || sortBy.isBlank()) ? "currentExposure" : sortBy;

        // Determine sort direction
        Sort.Direction direction = "desc".equalsIgnoreCase(sortOrder) ? Sort.Direction.DESC : Sort.Direction.ASC;

        // Build query with sorting and pagination
        Query query = new Query()
                .with(Sort.by(direction, sortField))
                .skip((long) page * size)
                .limit(size);

        // Run the query
        return mongoTemplate.find(query, AnalysisResult.class);
    }

}
