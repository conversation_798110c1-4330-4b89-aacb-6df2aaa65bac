package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.model.Country;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@RequiredArgsConstructor
public class CountriesRepository {

    private final MongoTemplate mongoTemplate;


    public List<Country> listCountries(String searchText, int page, int size) {
        Criteria criteria = new Criteria();

        if (searchText != null && !searchText.isBlank()) {
            criteria.orOperator(
                    Criteria.where("countryName").regex(searchText, "i"),
                    Criteria.where("countryCode").regex(searchText, "i")
            );
        }

        Query query = new Query(criteria)
                .skip((long) page * size)
                .limit(size);

        return mongoTemplate.find(query, Country.class);
    }

    public long countCountries(String searchText) {
        Criteria criteria = new Criteria();

        if (searchText != null && !searchText.isBlank()) {
            criteria.orOperator(
                    Criteria.where("countryName").regex(searchText, "i"),
                    Criteria.where("countryCode").regex(searchText, "i")
            );
        }

        Query query = new Query(criteria);
        return mongoTemplate.count(query, Country.class);
    }
}
