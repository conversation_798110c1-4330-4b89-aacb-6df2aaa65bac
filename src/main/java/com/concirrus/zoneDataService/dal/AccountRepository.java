package com.concirrus.zoneDataService.dal;

import com.concirrus.zoneDataService.dto.AccountFilterRequest;
import com.concirrus.zoneDataService.dto.AccountListDTO;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.model.Account;
import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.concirrus.zoneDataService.Constants.Constants.*;

@Repository
@RequiredArgsConstructor
public class AccountRepository {
    private static final String STATE_BOUND = "BOUND";
    private final MongoTemplate mongoTemplate;



    public List<Account> findBySubmissionIds(List<String> ids, String binder, int page, int size) {
        Query query = new Query();

        List<Criteria> criteriaList = new ArrayList<>();

        if (ids != null && !ids.isEmpty()) {
            criteriaList.add(Criteria.where("submissionId").in(ids));
        }

        if (binder != null && !binder.isEmpty()) {
            criteriaList.add(Criteria.where("binder").is(binder));
        }

        if (!criteriaList.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        // Pagination
        query.skip((long) page * size).limit(size);

        return mongoTemplate.find(query, Account.class);
    }

    public long countBySubmissionIds(List<String> ids, String binder) {
        Query query = new Query();

        List<Criteria> criteriaList = new ArrayList<>();

        if (ids != null && !ids.isEmpty()) {
            criteriaList.add(Criteria.where("submissionId").in(ids));
        }

        if (binder != null && !binder.isEmpty()) {
            criteriaList.add(Criteria.where("binder").is(binder));
        }

        if (!criteriaList.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        return mongoTemplate.count(query, Account.class);
    }


    public String getSubmissionIdByAccountId(String accountId) {
        if (accountId == null || accountId.isEmpty()) {
            throw new IllegalArgumentException("accountId cannot be null or empty");
        }

        Query query = new Query(Criteria.where("_id").is(accountId));
        query.fields().include("submissionId"); // Only retrieve submissionId

        Document doc = mongoTemplate.findOne(query, Document.class, "account");

        return doc != null ? doc.getString("submissionId") : null;
    }

    public List<String> getSubmissionIdsByAccountIds(List<String> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            throw new IllegalArgumentException("accountIds cannot be null or empty");
        }

        Query query = new Query(Criteria.where("_id").in(accountIds));
        query.fields().include("submissionId"); // Only retrieve submissionId

        List<Document> docs = mongoTemplate.find(query, Document.class, "account");

        return docs.stream()
                .map(doc -> doc.getString("submissionId"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public String getPolicyReferenceByAccountId(String accountId){
        if (accountId == null || accountId.isEmpty()) {
            throw new IllegalArgumentException("accountId cannot be null or empty");
        }

        Query query = new Query(Criteria.where("_id").is(accountId));
        query.fields().include("policyReference"); // Only retrieve submissionId

        Document doc = mongoTemplate.findOne(query, Document.class, "account");

        return doc != null ? doc.getString("policyReference") : null;
    }

    public List<AccountListDTO> listAccountNames(int page, int size, String searchText, boolean isBinderView, String sortBy, String sortOrder) {
        List<AggregationOperation> pipeline = new ArrayList<>();

        // Match stage
        Criteria criteria = Criteria.where("state").is(STATE_BOUND);
        if (StringUtils.hasText(searchText)) {
            criteria = criteria.and("accountName").regex(searchText, "i");
        }

        // if true then fetch account where binder = ACU Declaration
        if (isBinderView) {
            criteria = criteria.and(BINDER).is(ACU_DECLARATION);
        }

        pipeline.add(Aggregation.match(criteria));

        // Group by accountName to get distinct values
        pipeline.add(Aggregation.group("accountName")
                .first("submissionId").as("submissionId")); // or use max/min if multiple

        // Project the output fields
        pipeline.add(Aggregation.project()
                .and("_id").as("accountName")
                .and("submissionId").as("submissionId")
                .andExclude("_id"));

        // Add sorting before pagination
        if (StringUtils.hasText(sortBy)) {
            // Determine sort direction
            Sort.Direction direction = Objects.equals(DESC, sortOrder) ?
                    Sort.Direction.DESC : Sort.Direction.ASC;

            pipeline.add(Aggregation.sort(Sort.by(direction, sortBy)));
        }

        // Pagination
        pipeline.add(Aggregation.skip((long) page * size));
        pipeline.add(Aggregation.limit(size));

        Aggregation aggregation = Aggregation.newAggregation(pipeline);
        AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, "account", Document.class);

        return results.getMappedResults().stream()
                .map(doc -> {
                    AccountListDTO dto = new AccountListDTO();
                    dto.setAccountName(doc.getString("accountName"));
                    dto.setAccountId(doc.getString("submissionId"));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public long countAccountNames(String searchText, boolean isBinderView) {
        Query query = new Query();
        query.addCriteria(Criteria.where("state").is(STATE_BOUND));

        if (StringUtils.hasText(searchText)) {
            query.addCriteria(Criteria.where("accountName").regex(searchText, "i"));
        }

        // if true then count account where binder = ACU Declaration
        if (isBinderView) {
            query.addCriteria(Criteria.where(BINDER).is(ACU_DECLARATION));
        }

        List<String> distinctNames = mongoTemplate
                .findDistinct(query, "accountName", "account", String.class);

        return distinctNames.size();
    }

    public List<String> findDistinctAccountIdsFilterByStateAndCoverTpe(String state, String coverType) {
        Query query = new Query();
        query.addCriteria(Criteria.where("state").is(state));


        query.addCriteria(Criteria.where("binder").is(coverType));

        List<String> distinctAccountIds = mongoTemplate
                .findDistinct(query, "submissionId", "account", String.class);

        return distinctAccountIds;
    }

    public CustomPageResponse<Account> listAccountsSortedFlexible(
            AccountFilterRequest filterRequest,
            int page,
            int size,
            String sortField,
            String sortOrder
    ) {
        Criteria accountCriteria = new Criteria();
        accountCriteria.and("state").is(STATE_BOUND);

        if (StringUtils.hasText(filterRequest.getBinder())) {
            accountCriteria.and("binder").is(filterRequest.getBinder());
        }

        MatchOperation matchAccount = Aggregation.match(accountCriteria);

        LookupOperation lookup = LookupOperation.newLookup()
                .from("blast_zone_location_mapping")
                .localField("submissionId")
                .foreignField("submissionId")
                .as("mappings");

        // Unwind mappings to access and filter
        UnwindOperation unwind = Aggregation.unwind("mappings");

        // Filter blast zone + optional filters
        List<Criteria> mappingFilters = new ArrayList<>();
        mappingFilters.add(Criteria.where("mappings.blastZoneId").is(Long.parseLong(filterRequest.getBlastZoneId())));
        if (StringUtils.hasText(filterRequest.getPml())) {
            mappingFilters.add(Criteria.where("mappings.pmlZone").is(filterRequest.getPml()));
        }
        if (StringUtils.hasText(filterRequest.getGeoCoding())) {
            mappingFilters.add(Criteria.where("mappings.geocodingGrade").is(filterRequest.getGeoCoding()));
        }
        MatchOperation matchMappings = Aggregation.match(new Criteria().andOperator(mappingFilters.toArray(new Criteria[0])));

        // Group by _id and collect total PML + TIV
        GroupOperation group = Aggregation.group("_id")
                .first("accountName").as("accountName")
                .first("submissionId").as("submissionId")
                .first("binder").as("binder")
                .first("inceptionDate").as("inceptionDate")
                .first("expiryDate").as("expiryDate")
                .first("premium").as("premium")
                .first("line").as("line")
                .first("limit").as("limit")
                .first("deductible").as("deductible")
                .first("excess").as("excess")
                .first("policyReference").as("policyReference")
                .sum("mappings.blastZonePml").as("pml")
                .sum("mappings.blastZoneTiv").as("tiv");

        // Sort
        Sort.Direction direction = DESC.equalsIgnoreCase(sortOrder)? Sort.Direction.DESC: Sort.Direction.ASC;
        SortOperation sortOp = Aggregation.sort(Sort.by(direction, sortField));

        // Pagination
        SkipOperation skip = Aggregation.skip((long) page * size);
        LimitOperation limit = Aggregation.limit(size);

        Aggregation aggregation = Aggregation.newAggregation(
                matchAccount,
                lookup,
                unwind,
                matchMappings,
                group,
                sortOp,
                skip,
                limit
        );

        AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, "account", Document.class);
        List<Document> docs = results.getMappedResults();

        List<Account> accounts = docs.stream().map(doc -> {
            Account acc = new Account();
            Object idValue = doc.get("_id");
            if (idValue instanceof ObjectId) {
                acc.setId(((ObjectId) idValue).toHexString());
            } else if (idValue instanceof String) {
                acc.setId((String) idValue);
            } else {
                throw new IllegalStateException("_id is neither ObjectId nor String: " + idValue);
            }
            acc.setPolicyReference(doc.getString("policyReference"));
            acc.setLimit(doc.getDouble("limit"));
            acc.setDeductible(doc.getDouble("deductible"));
            acc.setExcess(doc.getDouble("excess"));
            acc.setLine(doc.getDouble("line"));
            acc.setAccountName(doc.getString("accountName"));
            acc.setSubmissionId(doc.getString("submissionId"));
            acc.setBinder(doc.getString("binder"));
            acc.setInceptionDate(doc.getString("inceptionDate"));
            acc.setExpiryDate(doc.getString("expiryDate"));
            acc.setPremium(doc.getDouble("premium"));
            acc.setPml(doc.getDouble("pml"));
            acc.setTiv(doc.getDouble("tiv"));
            return acc;
        }).collect(Collectors.toList());

        // Count total distinct accounts in blast zone
        long total = countAccountsInBlastZone(filterRequest);
        boolean hasMore = total > ((long) (page + 1) * size);

        return new CustomPageResponse<>(total, hasMore, page, size, accounts);
    }

    public long countAccountsInBlastZone(AccountFilterRequest filterRequest) {

        Criteria accountCriteria = new Criteria("state").is(STATE_BOUND);
        MatchOperation matchAccount = Aggregation.match(accountCriteria);

        LookupOperation lookup = LookupOperation.newLookup()
                .from("blast_zone_location_mapping")
                .localField("submissionId")
                .foreignField("submissionId")
                .as("mappings");

        UnwindOperation unwind = Aggregation.unwind("mappings");

        List<Criteria> mappingFilters = new ArrayList<>();
        mappingFilters.add(Criteria.where("mappings.blastZoneId").is(Long.parseLong(filterRequest.getBlastZoneId())));
        if (StringUtils.hasText(filterRequest.getPml())) {
            mappingFilters.add(Criteria.where("mappings.pmlZone").is(filterRequest.getPml()));
        }
        if (StringUtils.hasText(filterRequest.getGeoCoding())) {
            mappingFilters.add(Criteria.where("mappings.geocodingGrade").is(filterRequest.getGeoCoding()));
        }

        MatchOperation match = Aggregation.match(new Criteria().andOperator(mappingFilters.toArray(new Criteria[0])));
        GroupOperation group = Aggregation.group("_id");

        Aggregation aggregation = Aggregation.newAggregation(matchAccount, lookup, unwind, match, group);
        AggregationResults<Document> result = mongoTemplate.aggregate(aggregation, "account", Document.class);

        return result.getMappedResults().size();
    }


    public CustomPageResponse<Account> listAccountsSortedFlexibleV2(
            AccountFilterRequest filterRequest,
            int page,
            int size,
            String sortField,
            String sortOrder,
            Boolean binderView
    ) {
        // Build account criteria with indexed fields first
        Criteria accountCriteria = new Criteria();
        accountCriteria.and("state").is(STATE_BOUND);

        if (binderView) {
            accountCriteria.and("binder").is(ACU_DECLARATION);
        }


        if (StringUtils.hasText(filterRequest.getBinder())) {
            accountCriteria.and("binder").is(filterRequest.getBinder());
        }

        // Build mapping criteria upfront
        List<Criteria> mappingFilters = new ArrayList<>();
        mappingFilters.add(Criteria.where("blastZoneId").is(Long.parseLong(filterRequest.getBlastZoneId())));
        if (StringUtils.hasText(filterRequest.getPml())) {
            mappingFilters.add(Criteria.where("pmlZone").is(filterRequest.getPml()));
        }
        if (StringUtils.hasText(filterRequest.getGeoCoding())) {
            mappingFilters.add(Criteria.where("geocodingGrade").is(filterRequest.getGeoCoding()));
        }
        Criteria mappingCriteria = new Criteria().andOperator(mappingFilters.toArray(new Criteria[0]));

        // Initial match on accounts
        MatchOperation matchAccount = Aggregation.match(accountCriteria);

        // Optimized lookup with pipeline to filter during join
        LookupOperation lookup = LookupOperation.newLookup()
                .from("blast_zone_location_mapping")
                .localField("submissionId")
                .foreignField("submissionId")
                .pipeline(
                        Aggregation.match(mappingCriteria) // Filter in the lookup pipeline
                )
                .as("mappings");

        // Add match to exclude accounts with no mappings after filtering
        MatchOperation matchHasMappings = Aggregation.match(
                Criteria.where("mappings").not().size(0)
        );

        // Unwind the filtered mappings
        UnwindOperation unwind = Aggregation.unwind("mappings");

        // Group by _id and collect total PML + TIV
        GroupOperation group = Aggregation.group("_id")
                .first("accountName").as("accountName")
                .first("submissionId").as("submissionId")
                .first("binder").as("binder")
                .first("inceptionDate").as("inceptionDate")
                .first("expiryDate").as("expiryDate")
                .first("premium").as("premium")
                .first("line").as("line")
                .first("limit").as("limit")
                .first("deductible").as("deductible")
                .first("excess").as("excess")
                .first("acuParticipationLinePercentage").as("acuParticipationLinePercentage")
                .first("policyReference").as("policyReference")
                .sum("mappings.blastZonePml").as("pml")
                .sum("mappings.blastZoneTiv").as("tiv");

        // Add facet for count and results to avoid separate count query
        FacetOperation facet = Aggregation.facet(
                        // Count pipeline
                        Aggregation.count().as("count")
                ).as("totalCount")
                .and(
                        // Results pipeline with sorting and pagination
                        Aggregation.sort(Sort.by(
                                DESC.equalsIgnoreCase(sortOrder) ? Sort.Direction.DESC : Sort.Direction.ASC,
                                sortField
                        )),
                        Aggregation.skip((long) page * size),
                        Aggregation.limit(size)
                ).as("results");

        // Build the aggregation pipeline
        Aggregation aggregation = Aggregation.newAggregation(
                matchAccount,
                lookup,
                matchHasMappings,
                unwind,
                group,
                facet
        );

        AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, "account", Document.class);
        Document result = results.getUniqueMappedResult();

        // Extract count and results from facet
        List<Document> countDocs = (List<Document>) result.get("totalCount");
        long total = countDocs.isEmpty() ? 0 : countDocs.getFirst().getInteger("count", 0);

        List<Document> resultDocs = (List<Document>) result.get("results");

        // Convert to Account objects
        List<Account> accounts = resultDocs.stream().map(doc -> {
            Account acc = new Account();
            Object idValue = doc.get("_id");
            if (idValue instanceof ObjectId) {
                acc.setId(((ObjectId) idValue).toHexString());
            } else if (idValue instanceof String) {
                acc.setId((String) idValue);
            } else {
                throw new IllegalStateException("_id is neither ObjectId nor String: " + idValue);
            }
            acc.setPolicyReference(doc.getString("policyReference"));
            acc.setLimit(doc.getDouble("limit"));
            acc.setDeductible(doc.getDouble("deductible"));
            acc.setExcess(doc.getDouble("excess"));
            acc.setLine(doc.getDouble("line"));
            acc.setAccountName(doc.getString("accountName"));
            acc.setSubmissionId(doc.getString("submissionId"));
            acc.setBinder(doc.getString("binder"));
            acc.setInceptionDate(doc.getString("inceptionDate"));
            acc.setExpiryDate(doc.getString("expiryDate"));
            acc.setPremium(doc.getDouble("premium"));
            acc.setAcuParticipationLinePercentage(doc.getDouble("acuParticipationLinePercentage"));
            acc.setPml(doc.getDouble("pml"));
            acc.setTiv(doc.getDouble("tiv"));
            return acc;
        }).collect(Collectors.toList());

        boolean hasMore = total > ((long) (page + 1) * size);

        return new CustomPageResponse<>(total, hasMore, page, size, accounts);
    }
}




