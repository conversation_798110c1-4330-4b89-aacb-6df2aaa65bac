//package com.concirrus.zoneDataService.dal;
//
//import com.concirrus.zoneDataService.model.BlastZoneAccountMapping;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository
//public class BlastZoneAccountMappingRepository {
//
//    private final MongoTemplate mongoTemplate;
//
//    public BlastZoneAccountMappingRepository( @Qualifier("blastZoneTemplate") MongoTemplate mongoTemplate) {
//        this.mongoTemplate = mongoTemplate;
//    }
//
//    public List<BlastZoneAccountMapping> findByBlastZoneIds(List<Long> blastZoneIds) {
//        if (blastZoneIds == null || blastZoneIds.isEmpty()) {
//            return List.of();
//        }
//        Query query = new Query(Criteria.where("blastZoneId").in(blastZoneIds));
//        return mongoTemplate.find(query, BlastZoneAccountMapping.class);
//    }
//
//    public List<BlastZoneAccountMapping> findByAccountIds(List<String> accountIds) {
//        if (accountIds == null || accountIds.isEmpty()) {
//            return List.of();
//        }
//        Query query = new Query(Criteria.where("accountId").in(accountIds));
//        return mongoTemplate.find(query, BlastZoneAccountMapping.class);
//    }
//}