package com.concirrus.zoneDataService.dal;


import com.concirrus.zoneDataService.model.AccountPmlProjection;
import lombok.RequiredArgsConstructor;
import org.bson.Document;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class BlastZoneLocationMappingRepository {

    private final MongoTemplate mongoTemplate;



    public List<AccountPmlProjection> getAccountPmlTotalsByBlastZoneId(
            Long blastZoneId,
            @Nullable String pmlZone,
            @Nullable String geocodingGrade
    ) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("blastZoneId").is(blastZoneId));

        if (pmlZone != null && !pmlZone.isEmpty()) {
            criteriaList.add(Criteria.where("pmlZone").is(pmlZone));
        }

        if (geocodingGrade != null && !geocodingGrade.isEmpty()) {
            criteriaList.add(Criteria.where("geocodingGrade").is(geocodingGrade));
        }

        MatchOperation match = Aggregation.match(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));

        GroupOperation group = Aggregation.group("submissionId")
                .sum("blastZonePml").as("totalPml")
                .sum("blastZoneTiv").as("totalTiv");  // Summing TIV

        ProjectionOperation project = Aggregation.project()
                .and("_id").as("submissionId")
                .and("totalPml").as("totalPml")
                .and("totalTiv").as("totalTiv"); // Projecting TIV

        Aggregation aggregation = Aggregation.newAggregation(match, group, project);

        return mongoTemplate.aggregate(aggregation, "blast_zone_location_mapping", AccountPmlProjection.class)
                .getMappedResults();
    }

    public List<Long> getBlastZoneIdsBySubmissionIds(List<String> submissionIds) {
        if (CollectionUtils.isEmpty(submissionIds)) {
            return Collections.emptyList();
        }

        Query query = new Query(Criteria.where("submissionId").in(submissionIds));
        query.fields().include("blastZoneId").exclude("_id"); // Only fetch blastZoneId

        List<Document> docs = mongoTemplate.find(query, Document.class, "blast_zone_location_mapping");

        return docs.stream()
                .map(doc -> doc.getLong("blastZoneId"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}
