package com.concirrus.zoneDataService.controller;


import com.concirrus.zoneDataService.dto.BlastZoneDTO;
import com.concirrus.zoneDataService.dto.BlastZoneFilterRequest;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import com.concirrus.zoneDataService.model.AnalysisResult;
import com.concirrus.zoneDataService.services.BlastZoneService;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.BadRequestException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/blast-zones")
@Slf4j
public class BlastZoneController {

    private final BlastZoneService blastZoneService;

    public BlastZoneController(BlastZoneService blastZoneService) {
        this.blastZoneService = blastZoneService;
    }

    @PostMapping("/list")
    public ResponseEntity<BasicResponse<?>> listBlastZones(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "exposure", required = false) String sortBy,
            @RequestParam(defaultValue = "DESC", required = false) String sortOrder,
            @RequestParam (defaultValue = "false") Boolean isBinderView,
            @RequestBody BlastZoneFilterRequest request
    ) {
        try {
            log.debug("Processing blast zone list request for client ID: {}", clientId);
            validateSortOrder(sortOrder);
            Pageable pageable = PageRequest.of(page, size);

            CustomPageResponse<BlastZoneDTO> results = blastZoneService.listBlastZones(request, sortBy, sortOrder, pageable, isBinderView);

            BasicResponse<CustomPageResponse<BlastZoneDTO>> response = new BasicResponse<CustomPageResponse<BlastZoneDTO>>().setStatus(200).setError(new ArrayList<>()).setResult(results);
            return ResponseEntity.ok(response);
        } catch (BadRequestException e) {
            BasicResponse<Object> response = new BasicResponse<>().setStatus(400).setError(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            log.error("Error in listBlastZones: {}", e.getMessage(), e);
            BasicResponse<Object> response = new BasicResponse<>().setStatus(500).setError("Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/search")
    public BasicResponse<?> searchBlastZones(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam(required = false, defaultValue = "0") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer size,
            @RequestParam(required = false) String searchText
    ) {
        log.debug("Processing blast zone search request for client ID: {}", clientId);
        // Simulate logic (replace with actual call to service layer)
        CustomPageResponse<BlastZoneDTO> results = blastZoneService.searchBlastZone(searchText, page, size);
        return new BasicResponse<CustomPageResponse<BlastZoneDTO>>().setStatus(200).setError(new ArrayList<>()).setResult(results);
    }

    @GetMapping("/{id}")
    public ResponseEntity<BasicResponse<?>> getBlastZoneById(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @PathVariable String id,
            @RequestParam(defaultValue = "false") boolean summary
    ) {
        log.debug("Processing get blast zone by ID request for client ID: {} and zone ID: {}", clientId, id);
        BlastZoneDTO blastZone = blastZoneService.getBlastZoneById(id, summary);
        return ResponseEntity.ok(new BasicResponse<>().setResult(blastZone).setStatus(200).setError(new ArrayList<>()));
    }

    @GetMapping("/marginal-impact")
    public ResponseEntity<BasicResponse<List<AnalysisResult>>> getMarginalImpact(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam(value = "quoteId", required = true) String quoteId,
            @RequestParam(defaultValue = "pml") String sortBy,
            @RequestParam(defaultValue = "ASC") String sortOrder,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            log.debug("Processing marginal impact request for client ID: {} and quote ID: {}", clientId, quoteId);
            validateSortOrder(sortOrder);
            List<AnalysisResult> data = blastZoneService.getMarginalImpactData(sortBy, sortOrder, page, size);
            BasicResponse<List<AnalysisResult>> response = new BasicResponse<List<AnalysisResult>>()
                    .setStatus(200)
                    .setError(new ArrayList<>())
                    .setResult(data);
            return ResponseEntity.ok(response);
        } catch (BadRequestException e) {
            BasicResponse<List<AnalysisResult>> response = new BasicResponse<List<AnalysisResult>>()
                    .setStatus(400)
                    .setError(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            log.error("Error in getMarginalImpact: {}", e.getMessage(), e);
            BasicResponse<List<AnalysisResult>> response = new BasicResponse<List<AnalysisResult>>()
                    .setStatus(500)
                    .setError("Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    private void validateSortOrder(String sortOrder) throws BadRequestException {
        if (sortOrder != null && !"ASC".equalsIgnoreCase(sortOrder) && !"DESC".equalsIgnoreCase(sortOrder)) {
            throw new BadRequestException("Invalid sortOrder. Must be ASC or DESC");
        }
    }

    @PostMapping("/update-risk-score")
    public BasicResponse<String> updateRiskScores(@RequestHeader(value = CLIENT_ID) String clientId) {
        log.debug("Processing update risk scores request for client ID: {}", clientId);
        String result = blastZoneService.updateAllBlastZoneRiskScores();
        return new BasicResponse<String>().setStatus(200).setResult(result).setError(new ArrayList<>());
    }

}