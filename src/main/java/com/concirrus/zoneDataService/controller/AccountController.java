package com.concirrus.zoneDataService.controller;

import com.concirrus.zoneDataService.dto.AccountFilterRequest;
import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import com.concirrus.zoneDataService.services.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.BadRequestException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;

@RestController
@RequestMapping("/account")
@Slf4j
public class AccountController {

    private final AccountService accountService;

    public AccountController(AccountService accountService) {
        this.accountService = accountService;
    }

    @PostMapping("/list")
    public ResponseEntity<BasicResponse<?>> searchAccounts(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam (defaultValue = "0") int page,
            @RequestParam (defaultValue = "5") int size,
            @RequestParam(defaultValue = "premium") String sortBy,
            @RequestParam(defaultValue = "ASC") String sortOrder,
            @RequestParam (defaultValue = "false") Boolean isBinderView,
            @RequestBody AccountFilterRequest accountFilterRequest
    ) {
        try {
            log.debug("Processing account search request for client ID: {}", clientId);
            validateSortOrder(sortOrder);
            BasicResponse<Object> response = new BasicResponse<Object>().setStatus(200).setResult(accountService.getAllAccountsInBlastZone(accountFilterRequest,page,size,sortBy,sortOrder, isBinderView));
            return ResponseEntity.ok(response);
        } catch (BadRequestException e) {
            BasicResponse<Object> response = new BasicResponse<Object>().setStatus(400).setError(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            log.error("Error in searchAccounts: {}", e.getMessage(), e);
            BasicResponse<Object> response = new BasicResponse<>().setStatus(500).setError("Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    private void validateSortOrder(String sortOrder) throws BadRequestException {
        if (sortOrder != null && !"ASC".equalsIgnoreCase(sortOrder) && !"DESC".equalsIgnoreCase(sortOrder)) {
            throw new BadRequestException("Invalid sortOrder. Must be ASC or DESC");
        }
    }
}