package com.concirrus.zoneDataService.controller;


import com.concirrus.zoneDataService.dto.LocationFilterRequest;
import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import com.concirrus.zoneDataService.services.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.BadRequestException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;

@RestController
@RequestMapping("/locations")
@Slf4j
public class LocationController {

    private final LocationService locationService;

    public LocationController(LocationService locationService) {
        this.locationService = locationService;
    }

    @PostMapping("/list")
    public ResponseEntity<BasicResponse<?>> listLocations(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortOrder,
            @RequestBody LocationFilterRequest filterRequest
    ) {
        try {
            log.debug("Processing location list request for client ID: {}", clientId);
            validateSortOrder(sortOrder);
            BasicResponse<Object> response = new BasicResponse<Object>().setStatus(200).setResult(locationService.listLocations(filterRequest, sortBy, sortOrder, page, size));
            return ResponseEntity.ok(response);
        } catch (BadRequestException e) {
            BasicResponse<Object> response = new BasicResponse<>().setStatus(400).setError(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            log.error("Error in listLocations: {}", e.getMessage(), e);
            BasicResponse<Object> response = new BasicResponse<>().setStatus(500).setError("Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    private void validateSortOrder(String sortOrder) throws BadRequestException {
        if (sortOrder != null && !"ASC".equalsIgnoreCase(sortOrder) && !"DESC".equalsIgnoreCase(sortOrder)) {
            throw new BadRequestException("Invalid sortOrder. Must be ASC or DESC");
        }
    }
}
