package com.concirrus.zoneDataService.controller;

import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob;
import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisResult;
import com.concirrus.zoneDataService.dto.rest.CustomPageResponse;
import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import com.concirrus.zoneDataService.services.MiAnalysisProxyService;
import com.concirrus.zoneDataService.sal.MiAnalysisSal;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;

/**
 * MI Analysis Controller Proxy
 * 
 * Proxies MI Analysis requests from zone-data-service to blast-zone-processor service.
 * All endpoints require 'client-id' header for tenant identification.
 * 
 * Example usage:
 * curl -H "client-id: your-client-id" -X GET "/mi-analysis/jobs?submissionId=sub123&quoteId=quote456"
 */
@RestController
@RequestMapping("/mi-analysis")
@Validated
@Slf4j
@RequiredArgsConstructor
public class MiAnalysisController {

    private final MiAnalysisProxyService miAnalysisProxyService;

    /**
     * Get MI analysis job for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param submissionId Submission ID for job lookup
     * @param quoteId Quote ID for job lookup
     * @return MI analysis job details or error response
     */
    @GetMapping("/jobs")
    public ResponseEntity<BasicResponse<?>> getMiAnalysisJob(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam("submissionId") @NotBlank(message = "Submission ID cannot be blank") 
            @Size(max = 50, message = "Submission ID cannot exceed 50 characters") 
            @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "Submission ID can only contain alphanumeric characters, underscores, and hyphens") String submissionId,
            @RequestParam("quoteId") @NotBlank(message = "Quote ID cannot be blank") 
            @Size(max = 50, message = "Quote ID cannot exceed 50 characters") 
            @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "Quote ID can only contain alphanumeric characters, underscores, and hyphens") String quoteId) {

        log.info("Received MI analysis job request for clientId: {}, submissionId: {}, quoteId: {}", 
                clientId, submissionId, quoteId);

        try {
            Map<String, String> job = miAnalysisProxyService.getMiAnalysisJob(clientId, submissionId, quoteId);
            if (job == null) {
                log.warn("MI analysis job not found for clientId: {}, submissionId: {}, quoteId: {}", 
                        clientId, submissionId, quoteId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(BasicResponse.error("Job not found for the given submissionId and quoteId")
                        .setStatus(HttpStatus.NOT_FOUND.value()));
            }

            log.info("Successfully retrieved MI analysis job for clientId: {}", clientId);
            return ResponseEntity.ok(BasicResponse.result(job).setStatus(HttpStatus.OK.value()));
            
        } catch (MiAnalysisSal.MiAnalysisException e) {
            log.error("Error retrieving MI analysis job for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.error("Failed to retrieve MI analysis job: " + e.getMessage())
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        } catch (Exception e) {
            log.error("Unexpected error retrieving MI analysis job for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.error("An unexpected error occurred")
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * Get MI analysis results for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param jobId Job ID for analysis results
     * @param sortBy Sort field (default: currentExposure)
     * @param sortOrder Sort order (default: DESC)
     * @param page Page number (default: 0)
     * @param pageSize Page size (default: 20)
     * @param peril Optional peril filter
     * @return Paginated MI analysis results
     */
    @GetMapping
    public ResponseEntity<BasicResponse<CustomPageResponse<MiAnalysisResult>>> getMiAnalysis(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam("jobId") String jobId,
            @RequestParam(value = "sortBy", defaultValue = "currentExposure") String sortBy,
            @RequestParam(value = "sortOrder", defaultValue = "DESC") String sortOrder,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "pageSize", defaultValue = "20") int pageSize,
            @RequestParam(value = "peril", required = false) String peril) {

        log.info("Received MI analysis results request for clientId: {}, jobId: {}, peril: {}, page: {}, pageSize: {}", 
                clientId, jobId, peril, page, pageSize);

        try {
            CustomPageResponse<MiAnalysisResult> results = miAnalysisProxyService.getMiAnalysisResults(
                    clientId, jobId, sortBy, sortOrder, page, pageSize, peril);

            log.info("Successfully retrieved MI analysis results for clientId: {}, jobId: {}", clientId, jobId);
            return ResponseEntity.ok(BasicResponse.result(results).setStatus(HttpStatus.OK.value()));
            
        } catch (MiAnalysisSal.MiAnalysisException e) {
            log.error("Error retrieving MI analysis results for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.<CustomPageResponse<MiAnalysisResult>>error("Failed to retrieve MI analysis results: " + e.getMessage())
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        } catch (Exception e) {
            log.error("Unexpected error retrieving MI analysis results for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.<CustomPageResponse<MiAnalysisResult>>error("An unexpected error occurred")
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * Create MI analysis job for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param request MI analysis job request
     * @return Created job details
     */
    @PostMapping("/jobs")
    public ResponseEntity<BasicResponse<?>> createMiAnalysisJob(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestBody MiAnalysisJobRequest request) {

        log.info("Received MI analysis job creation request for clientId: {}, request: {}", clientId, request);

        try {
            MiAnalysisJob result = miAnalysisProxyService.createMiAnalysisJob(clientId, request);
            log.info("Successfully created MI analysis job for clientId: {}", clientId);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(BasicResponse.result(result).setStatus(HttpStatus.CREATED.value()));
                    
        } catch (MiAnalysisSal.MiAnalysisException e) {
            log.error("Error creating MI analysis job for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(BasicResponse.error("Failed to create MI analysis job: " + e.getMessage())
                    .setStatus(HttpStatus.BAD_REQUEST.value()));
        } catch (Exception e) {
            log.error("Unexpected error creating MI analysis job for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.error("An unexpected error occurred")
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * Execute MI analysis job for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param jobId Job ID to execute
     * @return Execution result
     */
    @PostMapping("/job/{jobId}")
    public ResponseEntity<BasicResponse<?>> executeMiAnalysisJob(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @PathVariable String jobId) {

        log.info("Received MI analysis job execution request for clientId: {}, jobId: {}", clientId, jobId);

        try {
            String result = miAnalysisProxyService.executeMiAnalysisJob(clientId, jobId);
            log.info("Successfully executed MI analysis job for clientId: {}, jobId: {}", clientId, jobId);
            return ResponseEntity.ok(BasicResponse.result(result).setStatus(HttpStatus.OK.value()));
            
        } catch (MiAnalysisSal.MiAnalysisException e) {
            log.error("Error executing MI analysis job for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.error("Failed to execute MI analysis job: " + e.getMessage())
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        } catch (Exception e) {
            log.error("Unexpected error executing MI analysis job for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.error("An unexpected error occurred")
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * Delete MI analysis job for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param submissionId Submission ID for job identification
     * @param quoteId Quote ID for job identification
     * @return Deletion status
     */
    @DeleteMapping
    public ResponseEntity<BasicResponse<String>> deleteMiAnalysisJob(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam("submissionId") @NotBlank(message = "Submission ID cannot be blank") 
            @Size(max = 50, message = "Submission ID cannot exceed 50 characters") 
            @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "Submission ID can only contain alphanumeric characters, underscores, and hyphens") String submissionId,
            @RequestParam("quoteId") @NotBlank(message = "Quote ID cannot be blank") 
            @Size(max = 50, message = "Quote ID cannot exceed 50 characters") 
            @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "Quote ID can only contain alphanumeric characters, underscores, and hyphens") String quoteId) {

        log.info("Received MI analysis job deletion request for clientId: {}, submissionId: {}, quoteId: {}", 
                clientId, submissionId, quoteId);

        try {
            String result = miAnalysisProxyService.deleteMiAnalysisJob(clientId, submissionId, quoteId);
            log.info("Successfully deleted MI analysis job for clientId: {}", clientId);
            return ResponseEntity.ok(BasicResponse.result(result).setStatus(HttpStatus.OK.value()));
            
        } catch (MiAnalysisSal.MiAnalysisException e) {
            log.error("Error deleting MI analysis job for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.<String>error("Failed to delete MI analysis job: " + e.getMessage())
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        } catch (Exception e) {
            log.error("Unexpected error deleting MI analysis job for clientId: {}", clientId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BasicResponse.<String>error("An unexpected error occurred")
                    .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
}
