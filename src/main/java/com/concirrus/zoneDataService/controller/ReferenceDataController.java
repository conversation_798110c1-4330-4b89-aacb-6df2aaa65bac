package com.concirrus.zoneDataService.controller;


import com.concirrus.zoneDataService.dto.submission.BasicResponse;
import com.concirrus.zoneDataService.services.AccountService;
import com.concirrus.zoneDataService.services.ReferenceDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.concirrus.zoneDataService.Constants.Constants.CLIENT_ID;

@RestController
@RequestMapping("/reference")
@Slf4j
public class ReferenceDataController {

    private  final  AccountService accountService;
    private final ReferenceDataService referenceDataService;

    public ReferenceDataController(AccountService accountService, ReferenceDataService referenceDataService) {
        this.accountService = accountService;
        this.referenceDataService = referenceDataService;
    }

    @GetMapping("/account/search")
    public BasicResponse<Object>getAccountNames(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(value = "searchText", required = false) String searchText,
            @RequestParam(required = false) boolean isBinderView,
            @RequestParam(defaultValue = "accountName") String sortBy,
            @RequestParam(defaultValue = "ASC") String sortOrder){
        log.debug("Processing account names search request for client ID: {}", clientId);
        return new BasicResponse<>().setStatus(200).setResult(accountService.listAccountNames(page,size,searchText, isBinderView, sortBy, sortOrder));
    }

    @GetMapping("/countries/search")
    public BasicResponse<Object>getCountries(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(value = "searchText", required = false) String searchText){
        log.debug("Processing countries search request for client ID: {}", clientId);
        return new BasicResponse<>().setStatus(200).setResult(referenceDataService.listCountries(page,size,searchText));
    }

    @GetMapping("/pml-zone")
    public BasicResponse<Object>getPmlZones(@RequestHeader(value = CLIENT_ID) String clientId){
        log.debug("Processing PML zones request for client ID: {}", clientId);
        return new BasicResponse<>().setStatus(200).setResult(referenceDataService.listPmlZones());
    }
}
