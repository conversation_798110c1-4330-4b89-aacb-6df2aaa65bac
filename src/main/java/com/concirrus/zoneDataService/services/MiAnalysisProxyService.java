package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJob;
import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.zoneDataService.dto.miAnalysis.MiAnalysisResult;
import com.concirrus.zoneDataService.dto.rest.CustomPageResponse;
import com.concirrus.zoneDataService.sal.MiAnalysisSal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Proxy service for MI Analysis operations
 * Acts as a bridge between zone-data-service controllers and blast-zone-processor service
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MiAnalysisProxyService {
    
    private final MiAnalysisSal miAnalysisSal;
    
    /**
     * Get MI analysis job by submission ID and quote ID
     */
    public Map<String, String> getMiAnalysisJob(String clientId, String submissionId, String quoteId) {
        log.info("Proxying MI analysis job request for clientId: {}, submissionId: {}, quoteId: {}", 
                clientId, submissionId, quoteId);
        return miAnalysisSal.getMiAnalysisJob(clientId, submissionId, quoteId);
    }
    
    /**
     * Get MI analysis results with pagination
     */
    public CustomPageResponse<MiAnalysisResult> getMiAnalysisResults(String clientId, String jobId, 
                                                                    String sortBy, String sortOrder, 
                                                                    int page, int pageSize, String peril) {
        log.info("Proxying MI analysis results request for clientId: {}, jobId: {}, peril: {}, page: {}, pageSize: {}", 
                clientId, jobId, peril, page, pageSize);
        return miAnalysisSal.getMiAnalysisResults(clientId, jobId, sortBy, sortOrder, page, pageSize, peril);
    }
    
    /**
     * Create MI analysis job
     */
    public MiAnalysisJob createMiAnalysisJob(String clientId, MiAnalysisJobRequest request) {
        log.info("Proxying MI analysis job creation for clientId: {}, request: {}", clientId, request);
        return miAnalysisSal.createMiAnalysisJob(clientId, request);
    }
    
    /**
     * Execute MI analysis job
     */
    public String executeMiAnalysisJob(String clientId, String jobId) {
        log.info("Proxying MI analysis job execution for clientId: {}, jobId: {}", clientId, jobId);
        return miAnalysisSal.executeMiAnalysisJob(clientId, jobId);
    }
    
    /**
     * Delete MI analysis job
     */
    public String deleteMiAnalysisJob(String clientId, String submissionId, String quoteId) {
        log.info("Proxying MI analysis job deletion for clientId: {}, submissionId: {}, quoteId: {}", 
                clientId, submissionId, quoteId);
        return miAnalysisSal.deleteMiAnalysisJob(clientId, submissionId, quoteId);
    }
}
