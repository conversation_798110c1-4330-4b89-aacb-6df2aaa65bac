package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.AccountRepository;
import com.concirrus.zoneDataService.dal.BlastZoneLocationMappingRepository;
import com.concirrus.zoneDataService.dto.AccountFilterRequest;
import com.concirrus.zoneDataService.dto.AccountListDTO;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.model.Account;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.concirrus.zoneDataService.Constants.Constants.ACU_DECLARATION;

@Slf4j
@Service
public class AccountService {

    private final AccountRepository accountRepository;
    private final BlastZoneLocationMappingRepository locationMappingRepository;

    public AccountService(AccountRepository accountRepository, BlastZoneLocationMappingRepository locationMappingRepository) {
        this.accountRepository = accountRepository;
        this.locationMappingRepository = locationMappingRepository;
    }

    public CustomPageResponse<Account> getAllAccountsInBlastZone(AccountFilterRequest accountFilterRequest, int page, int size, String sortBy, String sortOrder, Boolean isBinderView) {
        CustomPageResponse<Account> response = accountRepository.listAccountsSortedFlexibleV2(accountFilterRequest, page, size, sortBy, sortOrder, isBinderView);

        // Iterate over accounts and update exposure if needed
        for (Account acc : response.getData()) {
            Double tiv = acc.getTiv();
            Double pmlTiv = acc.getPml();
            Double limit = acc.getLimit();
            Double excess = acc.getExcess();
            Double deductible = acc.getDeductible();
            double line = acc.getLine()!=null ? acc.getLine() : 0.0;
            double appliedPanelShare = acc.getAcuParticipationLinePercentage()!=null ? acc.getAcuParticipationLinePercentage() : 0.0;
            if (tiv == null || limit == null || excess == null || deductible == null) continue;

            double calculatedExposure;
            double excessPlusDeductible = excess + deductible;
            if (tiv > (limit + excessPlusDeductible)) {
                calculatedExposure = limit.intValue();
            } else if (tiv < (excess + deductible)) {
                calculatedExposure = 0;
            } else {
                calculatedExposure = (double) Math.round(tiv - (excess + deductible));
            }

            double pmlExposure;
            if (pmlTiv > (limit + excessPlusDeductible)) {
                pmlExposure = limit.intValue();
            } else if (pmlTiv <= excessPlusDeductible) {
                pmlExposure = 0.0;
            } else {
                pmlExposure = Math.round(pmlTiv - excessPlusDeductible);
            }

            double acuExposure = calculatedExposure * (line / 100.0);

            double acuPmlExposure = pmlExposure * (line / 100.0);
            double binderAcuExposure = 0.0;

            if(acc.getBinder().equalsIgnoreCase(ACU_DECLARATION)) {
                binderAcuExposure = acuExposure - (acuExposure * (appliedPanelShare / 100));
                acuExposure = acuExposure * (appliedPanelShare / 100);
                acuPmlExposure = acuPmlExposure * (appliedPanelShare / 100);

            }

            acc.setExposure(acuExposure);
            acc.setPml(acuPmlExposure);
            acc.setBinderExposure(binderAcuExposure);
        }
        return response;
    }


    public CustomPageResponse<AccountListDTO>listAccountNames(int page, int size, String searchText, boolean isBinderView, String sortBy, String sortOrder) {
        log.info("listAccountNames - {}, {} {}, {}", page, size, searchText, isBinderView);
        List<AccountListDTO>accountListDTOS = accountRepository.listAccountNames(page,size,searchText, isBinderView, sortBy, sortOrder);
        long total = accountRepository.countAccountNames(searchText, isBinderView);
        boolean hasMore = total > (long) (1 + page) *size;

        return new CustomPageResponse<>(total,hasMore,page,size,accountListDTOS);
    }
}
