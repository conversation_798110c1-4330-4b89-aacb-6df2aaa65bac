package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.CountriesRepository;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.model.Country;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReferenceDataService {

    private final CountriesRepository countriesRepository;

    public ReferenceDataService(CountriesRepository countriesRepository) {
        this.countriesRepository = countriesRepository;
    }

    public CustomPageResponse<Country>listCountries( int page , int size, String searchText){
        List<Country>countries = countriesRepository.listCountries(searchText,page,size);
        long total = countriesRepository.countCountries(searchText);

        boolean hasMore = total > (long) (page + 1) *size;

        return new CustomPageResponse<>(total,hasMore,page,size,countries);
    }

    public List<String>listPmlZones(){
        return List.of("Zone A","Zone B","Zone C","Zone D","Zone E");
    }

}
