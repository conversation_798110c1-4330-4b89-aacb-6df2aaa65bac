package com.concirrus.zoneDataService.services;


import com.concirrus.zoneDataService.dal.AccountRepository;
import com.concirrus.zoneDataService.dal.LocationRepository;
import com.concirrus.zoneDataService.dto.CustomPageResponse;
import com.concirrus.zoneDataService.dto.LocationFilterRequest;
import com.concirrus.zoneDataService.dto.LocationResponse;
import org.apache.coyote.BadRequestException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class LocationService {

    private final LocationRepository locationRepository;
    private final AccountRepository accountRepository;

    public LocationService(LocationRepository locationRepository, AccountRepository accountRepository) {
        this.locationRepository = locationRepository;
        this.accountRepository = accountRepository;
    }

    public CustomPageResponse<LocationResponse> listLocations(LocationFilterRequest filterRequest,String sortBy, String sortOrder, int page, int size) throws BadRequestException {

        String submissionId = accountRepository.getSubmissionIdByAccountId(filterRequest.getAccountId());
        if (!StringUtils.hasText(submissionId)){
            throw new BadRequestException("Not a valid account");
        }
        filterRequest.setSubmissionId(submissionId);
        List<LocationResponse> locationResponses = locationRepository.fetchLocationResponsesByBlastZoneId(filterRequest,sortBy,sortOrder,page,size);
        String policyReference = accountRepository.getPolicyReferenceByAccountId(filterRequest.getAccountId());
        long count = locationRepository.countLocationResponsesByBlastZoneId(filterRequest);
        locationResponses.forEach(locationResponse -> locationResponse.setPolicyReference(policyReference));
        boolean hasMore = count > ((long) (page+1) *size);

        return new CustomPageResponse<>(count,hasMore,page,size,locationResponses);
    }

}
