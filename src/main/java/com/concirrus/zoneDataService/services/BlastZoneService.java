package com.concirrus.zoneDataService.services;

import com.concirrus.zoneDataService.dal.AccountRepository;
import com.concirrus.zoneDataService.dal.AnalysisResultRepository;
import com.concirrus.zoneDataService.dal.BlastZoneLocationMappingRepository;
import com.concirrus.zoneDataService.dal.BlastZoneRepository;
import com.concirrus.zoneDataService.dto.*;
import com.concirrus.zoneDataService.exception.ResourceNotFoundException;
import com.concirrus.zoneDataService.model.AnalysisResult;
import com.concirrus.zoneDataService.model.BlastZone;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import static com.concirrus.zoneDataService.Constants.Constants.ACU_DECLARATION;
import static com.concirrus.zoneDataService.Constants.Constants.STATE_BOUND;

@Slf4j
@Service
public class BlastZoneService {

    private final BlastZoneRepository blastZoneRepository;
    private final AccountRepository accountRepository;
    private final BlastZoneLocationMappingRepository locationMappingRepository;
    private final AnalysisResultRepository analysisResultRepository;
    // Hardcoded base URL variable
    private static final String SP_API_BASE_URL = "https://sp-api-url.com"; // Replace later

    public BlastZoneService(BlastZoneRepository blastZoneRepository, AccountRepository accountRepository, BlastZoneLocationMappingRepository locationMappingRepository, AnalysisResultRepository analysisResultRepository) {
        this.blastZoneRepository = blastZoneRepository;
        this.accountRepository = accountRepository;
        this.locationMappingRepository = locationMappingRepository;
        this.analysisResultRepository = analysisResultRepository;
    }

    public String updateAllBlastZoneRiskScores() {
        List<BlastZone> blastZones = blastZoneRepository.findAll();
        RestTemplate restTemplate = new RestTemplate();

        for (BlastZone zone : blastZones) {
            try {
                Map<String, Object> locationRequest = new HashMap<>();
                if (zone.getCentre() != null) {
                    locationRequest.put("Latitude", String.valueOf(zone.getCentre().getY())); // Y is latitude
                    locationRequest.put("Longitude", String.valueOf(zone.getCentre().getX())); // X is longitude
                } else {
                    log.warn("Centre is null for zone id: {}", zone.getId());
                    continue;
                }
                locationRequest.put("Id", zone.getId());

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);

                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(locationRequest, headers);

                // Use constant or field for base URL
                ResponseEntity<Map> response = restTemplate.postForEntity(SP_API_BASE_URL, entity, Map.class);

                if (response.getStatusCode().is2xxSuccessful()) {
                    Map<String, Object> body = response.getBody();
                    if (body != null && body.containsKey("Risk")) {
                        Map<String, Object> riskMap = (Map<String, Object>) body.get("Risk");

                        RiskDataDTO riskData = new RiskDataDTO();
                        if (riskMap.get("War") instanceof Number)
                            riskData.setWar(((Number) riskMap.get("War")).doubleValue());
                        if (riskMap.get("Terrorism") instanceof Number)
                            riskData.setTerrorism(((Number) riskMap.get("Terrorism")).doubleValue());
                        if (riskMap.get("CivilUnrest") instanceof Number)
                            riskData.setCivilUnrest(((Number) riskMap.get("CivilUnrest")).doubleValue());
                        if (riskMap.get("Combined") instanceof Number)
                            riskData.setCombined(((Number) riskMap.get("Combined")).doubleValue());

                        zone.setRiskData(riskData);
                        blastZoneRepository.save(zone);
                    }
                }
            } catch (Exception e) {
                log.error("Failed to update risk score for zone id: {}", zone.getId(), e);
            }
        }

        return "Successfully updated risk scores for all blast zones.";
    }


    public CustomPageResponse<BlastZoneDTO> listBlastZones(BlastZoneFilterRequest request, String sortBy, String sortOrder, Pageable pageable, Boolean isBinderView) {
        if (request.getAccounts() != null && !request.getAccounts().isEmpty()) {
            // Use mapping table to get BlastZone IDs for given accounts
            List<Long> blastZoneIds = locationMappingRepository.getBlastZoneIdsBySubmissionIds(request.getAccounts());
            return  blastZoneRepository.filterBlastZones(blastZoneIds,request.getCountry(),sortBy,sortOrder,pageable,getCollectionName(request));
        } else {
            if(!isBinderView) {
                // No account filter; query only blast zones
                return blastZoneRepository.filterBlastZones(null, request.getCountry(), sortBy, sortOrder, pageable, getCollectionName(request));
            }
            else {
                List<String> accounts =  accountRepository.findDistinctAccountIdsFilterByStateAndCoverTpe(STATE_BOUND, ACU_DECLARATION);
                List<Long> blastZoneIds = locationMappingRepository.getBlastZoneIdsBySubmissionIds(accounts);
                return  blastZoneRepository.filterBlastZones(blastZoneIds,request.getCountry(),sortBy,sortOrder,pageable,getCollectionName(request));
            }
        }
    }

    public CustomPageResponse<BlastZoneDTO>searchBlastZone(String searchText,int page, int size){
        Pageable pageable = PageRequest.of(page,size);
        return blastZoneRepository.searchBlastZones(searchText,pageable);
    }

    public BlastZoneDTO getBlastZoneById(String id,boolean summary){
        BlastZoneDTO blastZone = blastZoneRepository.findById(id);
        if (blastZone == null) {
            throw new ResourceNotFoundException("Blast Zone", id);
        }
        return blastZone;
    }

    public List<AnalysisResult> getMarginalImpactData(String sortBy, String sortOrder, int page, int size) {

        return analysisResultRepository.findAnalysisResults(sortBy,sortOrder,page,size);
    }

    private String getSortKey(String sortBy){
        if ("pml".equalsIgnoreCase(sortBy)||"exposure".equalsIgnoreCase(sortBy)){
            return sortBy;
        }
        return "pml";
    }

    private String getCollectionName(BlastZoneFilterRequest filterRequest){
        String blastZoneCollectionName = "blast_zones";
        LocalDate localDate = filterRequest.getRollUpDate();
        if (localDate!=null){
            String suffix = localDate.toString();
            log.info("Roll up date is {}",suffix);
            return blastZoneCollectionName+"_"+suffix;
        }
        return blastZoneCollectionName;
    }
}
