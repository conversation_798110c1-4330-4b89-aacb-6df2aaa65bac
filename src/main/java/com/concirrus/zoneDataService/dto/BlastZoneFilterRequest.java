package com.concirrus.zoneDataService.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Setter
@Getter
public class BlastZoneFilterRequest {
    private List<String> accounts;
    private List<String> country;
    private LocalDate rollUpDate;

    @Override
    public String toString() {
        return "accounts=" + accounts + ", country=" + country + ", rollUpDate=" + rollUpDate;
    }
}
