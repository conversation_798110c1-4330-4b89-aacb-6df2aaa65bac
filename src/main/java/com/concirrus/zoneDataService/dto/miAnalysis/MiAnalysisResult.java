package com.concirrus.zoneDataService.dto.miAnalysis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MiAnalysisResult {
    private String blastZoneId;
    private String blastZoneName;
    private Double currentExposure;
    private Double currentExposureUtilisation;
    private Double currentPmlContribution;
    private Double currentPmlUtilisation;
    private Double updatedExposure;
    private Double updatedExposureUtilisation;
    private Double accountContribution;
    private Double updatedPmlExposure;
}
