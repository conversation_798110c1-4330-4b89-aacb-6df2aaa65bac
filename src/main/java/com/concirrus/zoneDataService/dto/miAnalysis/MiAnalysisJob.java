package com.concirrus.zoneDataService.dto.miAnalysis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MiAnalysisJob {
    private String jobId;
    private String submissionId;
    private String quoteId;
    private String status;
    private Instant createdAt;
}
