package com.concirrus.zoneDataService.dto.miAnalysis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MiAnalysisJobRequest {
    private String submissionId;
    private String quoteId;
    private Double srcc;
    private Double war;
    @JsonProperty("sAndT")
    private Double sAndT;
    private List<String> perils;
    private Double excess;
    private Double line;
    private Double deductible;
    private Boolean initiateJob = true;
    private String binder;
    private Double acuParticipationLinePercentage;
}
