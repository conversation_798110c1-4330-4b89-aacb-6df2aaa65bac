package com.concirrus.zoneDataService.dto.miAnalysis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MiAnalysisJobRequest {
    private String submissionId;
    private String quoteId;
    private List<String> perils;
    private Double excess;
    private Double line;
    private Double deductible;
    private Double srcc;
    private Double war;
    private Double sAndT;
    private Boolean initiateJob;
}
