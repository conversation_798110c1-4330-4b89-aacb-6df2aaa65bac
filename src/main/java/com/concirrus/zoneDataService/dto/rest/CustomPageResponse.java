package com.concirrus.zoneDataService.dto.rest;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomPageResponse<T> {
    private List<T> content;
    private long totalElements;
    private int page;
    private int size;
    private boolean hasNext;

    public static <T> CustomPageResponse<T> of(List<T> content, long totalElements, int page, int size, boolean hasNext) {
        return new CustomPageResponse<>(content, totalElements, page, size, hasNext);
    }

    public static <T> CustomPageResponse<T> empty(int page, int size) {
        return new CustomPageResponse<>(List.of(), 0L, page, size, false);
    }
}
