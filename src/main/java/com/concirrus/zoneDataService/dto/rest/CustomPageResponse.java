package com.concirrus.zoneDataService.dto.rest;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomPageResponse<T> {
    private long total;
    private boolean hasMore;
    private int page;
    private int size;
    private List<T> data;

    public static <T> CustomPageResponse<T> empty(int page, int size) {
        CustomPageResponse<T> response = new CustomPageResponse<>();
        response.setData(List.of());
        response.setHasMore(false);
        response.setTotal(0);
        response.setPage(page);
        response.setSize(size);
        return response;
    }

    public static <T> CustomPageResponse<T> of(List<T> data, long total, int page, int size, boolean hasMore) {
        CustomPageResponse<T> response = new CustomPageResponse<>();
        response.setData(data);
        response.setTotal(total);
        response.setPage(page);
        response.setSize(size);
        response.setHasMore(hasMore);
        return response;
    }
}
