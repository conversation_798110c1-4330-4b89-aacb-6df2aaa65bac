package com.concirrus.zoneDataService.dto;

import com.concirrus.zoneDataService.model.BlastZone;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface MyMapper {

    BlastZone toEntity(BlastZoneDTO blastZoneDTO);

    @Mapping(source = "id", target = "id", numberFormat = "#")
    BlastZoneDTO fromEntity(BlastZone blastZone);
}
