package com.concirrus.zoneDataService.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class LocationResponse {

    private String policyReference;
    private String locationName;
    private String street;
    private String city;
    private Double latitude;
    private Double longitude;
    private String postCode;
    private String geocoding;
    private Double tiv;
    private String pmlZone;
    private Double pmlPercentage;
    private Double pmlTiv;
    private GeoJsonPoint centre;

    @JsonProperty("centre")
    public Map<String, Object> getFormattedCentre() {
        if (centre == null) return null;

        BigDecimal x = BigDecimal.valueOf(centre.getX());
        BigDecimal y = BigDecimal.valueOf(centre.getY());

        BigDecimal roundedX = x.setScale(5, RoundingMode.HALF_UP);
        BigDecimal roundedY = y.setScale(5, RoundingMode.HALF_UP);

        Map<String, Object> geoJson = new HashMap<>();
        geoJson.put("type", "Point");
        geoJson.put("x", x);
        geoJson.put("y", y);
        geoJson.put("coordinates", List.of(roundedX, roundedY));

        return geoJson;
    }
}
