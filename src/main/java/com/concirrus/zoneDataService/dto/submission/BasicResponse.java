package com.concirrus.zoneDataService.dto.submission;

import lombok.Data;
import org.springframework.http.HttpStatus;

import java.time.Instant;

@Data
public class BasicResponse<T>{
    private int status;
    private final Instant timestamp = Instant.now();
    private T result;
    private Object error;

    public BasicResponse() {
    }

    public static <T> BasicResponse<T> result(T result) {
        return (new BasicResponse<T>()).setResult(result);
    }

    public static BasicResponse<Object> error(Object error) {
        return (new BasicResponse<Object>()).setError(error);
    }

    public static BasicResponse<Object> status(HttpStatus status) {
        return (new BasicResponse<Object>()).setStatus(status.value());
    }

    public BasicResponse<T> setStatus(int status) {
        this.status = status;
        return this;
    }

    public BasicResponse<T> setResult(T result) {
        this.result = result;
        return this;
    }

    public BasicResponse<T> setError(Object error) {
        this.error = error;
        return this;
    }
}
