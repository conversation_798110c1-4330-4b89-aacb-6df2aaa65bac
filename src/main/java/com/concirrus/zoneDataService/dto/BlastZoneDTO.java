package com.concirrus.zoneDataService.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BlastZoneDTO {

    private String id;
    private String name;
    private String country;

    private GeoJsonPoint centre;

    @JsonProperty("centre")
    public Map<String, Object> getFormattedCentre() {
        if (centre == null) return null;

        BigDecimal x = BigDecimal.valueOf(centre.getX());
        BigDecimal y = BigDecimal.valueOf(centre.getY());

        BigDecimal roundedX = x.setScale(5, RoundingMode.HALF_UP);
        BigDecimal roundedY = y.setScale(5, RoundingMode.HALF_UP);

        Map<String, Object> geoJson = new HashMap<>();
        geoJson.put("type", "Point");
        geoJson.put("x", x);
        geoJson.put("y", y);
        geoJson.put("coordinates", List.of(roundedX, roundedY));

        return geoJson;
    }

    private Double tiv;
    private Double pml;
    private Double exposure;
    private Double geocodingResolution;
    private String geocoding;
    private Double pmlUtilisation;
    private Double dfUtilisation;
    private String firstExpiry;
    private Instant updatedOn;
    private RiskDataDTO riskData; // War, Terrorism, Civil Unrest, Combined
    private Double binderExposure;
    private Double binderDfUtilisation;

}
