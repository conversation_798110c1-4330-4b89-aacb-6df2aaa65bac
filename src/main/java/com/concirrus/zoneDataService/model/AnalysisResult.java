package com.concirrus.zoneDataService.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "analysis_result")
public class AnalysisResult {
    private String blastZoneId;
    private String blastZoneName;
    private Double currentExposure;
    private Double currentExposureUtilisation;
    private Double currentPmlContribution;
    private Double currentPmlUtilisation;
    private Double updatedExposure;
    private Double updatedExposureUtilisation;
    private Double accountContribution;
    private Double updatedPmlExposure;
}
