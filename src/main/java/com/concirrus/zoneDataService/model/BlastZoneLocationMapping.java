package com.concirrus.zoneDataService.model;

import lombok.Data;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "blast_zone_location_mapping")
public class BlastZoneLocationMapping {

    @Id
    private Long id;

    private Long blastZoneId;
    private String locationId;
    private String submissionId;

    private double distance;
    private String geocodingGrade;
    private double blastZonePml;// Probable Maximum Loss
    private double blastZoneTiv;
    private String pmlZone;
    private double pmlPercentage;
    private String state;
}