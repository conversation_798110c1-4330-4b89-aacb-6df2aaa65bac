package com.concirrus.zoneDataService.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexType;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "locations")
public class Location {

    @Id
    private String id;
    private String state;
    private String locationName;           // {locationName from location}
    private String street;                 // {street from location}
    private String city;                   // {postalCode from location}
    private Double latitude;
    private Double longitude;
    private String geocodingGrade;          // {grade from location}
    private Double geocodingResolution;     // {geocodingResolution}
    private String locationCurrency;
    private String postCode;
    @GeoSpatialIndexed(type = GeoSpatialIndexType.GEO_2DSPHERE)
    private GeoJsonPoint geometry;

    private Double biValue12Months;

    private Double biValue12MonthsOriginalCcy;

    private Double buildingValue;

    private Double buildingValueOriginalCcy;

    private Double contentsValue;

    private Double contentsValueOriginalCcy;

    private double tiv;
}

