
server:
  port: 8080
  servlet:
    context-path: /zone-data
    session:
      timeout: 180m

management:
  endpoints:
    web:
      base-path: /manage
      exposure:
        include: health, info, prometheus, restart
  endpoint:
    info:
      enabled: true
    health:
      enabled: true
      show-details: always
    restart:
      enabled: true

cloud:
  provider: aws
  aws:
    credentials:
      access-key:
      secret-key:
    region:
      static: us-east-2
  queue:
    in:
      submission-v2: submission-v2-dev
    out:
      submission-changes: ${SUBMISSION_CHANGES_QUEUE:submission-changes}

spring:
  data:
    mongodb:
      uri: mongodb://${MONGO_USERNAME:submission}:${MONGO_PASSWORD:Pfytak76NAEkjXo}@${MONGO_IPS:localhost:27017}/${AGGREGATION_MONGO_DB:geocoding_db}
  cloud:
    gcp:
      pubsub:
        enabled: false

  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB


mongo:
  db:
    uri: mongodb://localhost:27017
    blast-zones: ${AGGREGATION_MONGO_DB:blastZones}


namespace:
  submission: ${SUBMISSION_NAMESPACE:submission-dev}
  blast-zone-processor: ${BLAST_ZONE_PROCESSOR_NAMESPACE:blast-zone-processor-dev}

