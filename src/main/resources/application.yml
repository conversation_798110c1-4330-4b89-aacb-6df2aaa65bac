
server:
  port: 8080
  servlet:
    context-path: /zone-data
    session:
      timeout: 180m

management:
  endpoints:
    web:
      base-path: /manage
      exposure:
        include: health, info, prometheus, restart
  endpoint:
    info:
      enabled: true
    health:
      enabled: true
      show-details: always
    restart:
      enabled: true


spring:
  data:
    mongodb:
      uri: mongodb://${AGGREGATION_MONGO_USERNAME:submission}:${AGGREGATION_MONGO_PASSWORD}@${AGGREGATION_MONGO_IPS:localhost:27017}/${AGGREGATION_DB:aggregation_db}?authSource=admin
  cloud:
    gcp:
      pubsub:
        enabled: false
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB





namespace:
  submission: ${SUBMISSION_NAMESPACE:submission-dev}
  currency-service: ${CURRENCY_SERVICE_NAMESPACE:hull-mt-dev}
  blast-zone-processor: ${BLAST_ZONE_PROCESSOR_NAMESPACE:blast-zone-processor-dev}

tenant-ids: ${VALID_TENANT_IDS:26a926c5-2e2d-40bf-950b-1dddf41a3746}