FROM maven:3.9.4-eclipse-temurin-21-alpine
ENV JAVA_HOME=/opt/java/openjdk
ENV PATH="$JAVA_HOME/bin:$PATH"
VOLUME /tmp
EXPOSE 8080
ARG PROJECT_NAME
ARG PROJECT_VERSION=0.0.1-SNAPSHOT.jar
ARG CI_COMMIT_REF_NAME

WORKDIR /app
RUN chmod  -R 777 /app
RUN mkdir ~/.m2
ADD . .

# Copy the settings.xml file into the container
#COPY settings.xml /app/settings.xml

# Move the settings.xml file to the Maven default location
#RUN mv /app/settings.xml ~/.m2/settings.xml

RUN mvn clean install -DsharedLibsVersion=$CI_COMMIT_REF_NAME

RUN cp /app/target/$PROJECT_NAME-$PROJECT_VERSION /app/service.jar
RUN sh -c 'touch /service.jar'
ENV JAVA_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:InitiatingHeapOccupancyPercent=45"

ENTRYPOINT [ "sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar /app/service.jar" ]
