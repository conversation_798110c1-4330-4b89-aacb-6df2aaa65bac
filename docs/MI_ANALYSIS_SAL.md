# MI Analysis Service Access Layer (SAL)

This document describes the MI Analysis SAL implementation that enables the zone-data-service to make calls to the blast-zone-processor service for MI (Maximum Individual) analysis operations.

## Overview

The MI Analysis SAL follows the same pattern as the existing AccessManagementSal and provides a clean interface for making HTTP calls to the blast-zone-processor service. It includes proper error handling, validation, and logging.

## Components

### 1. DTOs (Data Transfer Objects)

Located in `src/main/java/com/concirrus/zoneDataService/dto/miAnalysis/`:

- **MiAnalysisJobRequest**: Request object for creating MI analysis jobs
- **MiAnalysisJob**: Response object representing an MI analysis job
- **MiAnalysisResult**: Individual MI analysis result
- **CustomPageResponse**: Paginated response wrapper

### 2. SAL Layer

**MiAnalysisSal** (`src/main/java/com/concirrus/zoneDataService/sal/MiAnalysisSal.java`):
- Handles HTTP communication with blast-zone-processor service
- Includes proper error handling and validation
- Uses RestTemplate for HTTP calls
- Follows the same pattern as AccessManagementSal

### 3. Proxy Service

**MiAnalysisProxyService** (`src/main/java/com/concirrus/zoneDataService/services/MiAnalysisProxyService.java`):
- Provides a clean interface for controllers
- Acts as a bridge between zone-data-service and blast-zone-processor
- Includes logging for all operations

### 4. Controller

**MiAnalysisController** (`src/main/java/com/concirrus/zoneDataService/controller/MiAnalysisController.java`):
- Exposes MI Analysis endpoints in zone-data-service
- Includes proper validation and error handling
- Requires `client-id` header for tenant identification

## Available Endpoints

All endpoints are prefixed with `/zone-data/mi-analysis` and require a `client-id` header.

### 1. Get MI Analysis Job
```
GET /jobs?submissionId={submissionId}&quoteId={quoteId}
```
Retrieves an MI analysis job by submission ID and quote ID.

### 2. Get MI Analysis Results
```
GET /?jobId={jobId}&sortBy={sortBy}&sortOrder={sortOrder}&page={page}&pageSize={pageSize}&peril={peril}
```
Retrieves paginated MI analysis results for a job.

### 3. Create MI Analysis Job
```
POST /jobs
Content-Type: application/json

{
  "submissionId": "sub123",
  "quoteId": "quote456",
  "perils": ["war", "srcc"],
  "excess": 1000000.0,
  "line": 0.5,
  "deductible": 50000.0,
  "war": 0.1,
  "srcc": 0.05,
  "initiateJob": true
}
```

### 4. Execute MI Analysis Job
```
POST /job/{jobId}
```
Starts processing an existing MI analysis job.

### 5. Delete MI Analysis Job
```
DELETE /?submissionId={submissionId}&quoteId={quoteId}
```
Deletes an MI analysis job.

## Configuration

Add the following configuration to your application properties:

```yaml
namespace:
  blast-zone-processor: ${BLAST_ZONE_PROCESSOR_NAMESPACE:blast-zone-processor-dev}
```

## Usage Example

```java
@Autowired
private MiAnalysisProxyService miAnalysisProxyService;

public void example() {
    String clientId = "your-client-id";
    
    // Create a job
    MiAnalysisJobRequest request = new MiAnalysisJobRequest();
    request.setSubmissionId("sub123");
    request.setQuoteId("quote456");
    request.setPerils(List.of("war", "srcc"));
    request.setExcess(1000000.0);
    request.setLine(0.5);
    
    MiAnalysisJob job = miAnalysisProxyService.createMiAnalysisJob(clientId, request);
    
    // Get results
    CustomPageResponse<MiAnalysisResult> results = miAnalysisProxyService.getMiAnalysisResults(
        clientId, job.getJobId(), "currentExposure", "DESC", 0, 20, null);
}
```

## Error Handling

The SAL includes comprehensive error handling:

- **Validation errors**: Thrown as `IllegalArgumentException`
- **HTTP errors**: Wrapped in `MiAnalysisException` with proper logging
- **Network errors**: Wrapped in `MiAnalysisException` with proper logging

## Testing

Unit tests are provided for:
- `MiAnalysisSalTest`: Tests the SAL layer
- `MiAnalysisProxyServiceTest`: Tests the proxy service

Run tests with:
```bash
mvn test -Dtest=MiAnalysisSalTest
mvn test -Dtest=MiAnalysisProxyServiceTest
```

## Service URL Pattern

The SAL constructs URLs using the pattern:
```
http://blast-zone-processor-service.{namespace}/mi-analysis/{endpoint}
```

Where `{namespace}` is configured via the `namespace.blast-zone-processor` property.
